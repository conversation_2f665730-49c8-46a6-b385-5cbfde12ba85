<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Failed suite [Default suite]" guice-stage="DEVELOPMENT">
  <test thread-count="5" name="Default test(failed)">
    <classes>
      <class name="com.GCash_GGivesScripts.TokenGCASH">
        <methods>
          <include name="EmptyClientSecret_TokenGCash"/>
          <include name="InvalidClientSecret_TokenGCash"/>
          <include name="EmptyGrantType_TokenGCash"/>
          <include name="InvalidClientId_TokenGCash"/>
          <include name="InvalidGrantType_TokenGCash"/>
          <include name="TokenGCash_200"/>
          <include name="EmptyClientId_TokenGCash"/>
        </methods>
      </class> <!-- com.GCash_GGivesScripts.TokenGCASH -->
    </classes>
  </test> <!-- Default test(failed) -->
</suite> <!-- Failed suite [Default suite] -->
