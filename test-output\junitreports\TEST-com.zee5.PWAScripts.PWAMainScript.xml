<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWAScripts.PWAMainScript" tests="8" failures="0" timestamp="4 May 2020 16:44:40 GMT" time="2663.772" errors="3">
  <testcase name="PWAConsumptionsScreen" time="431.684" classname="com.zee5.PWAScripts.PWAMainScript"/>
  <testcase name="PWAOnboarding" time="278.755" classname="com.zee5.PWAScripts.PWAMainScript"/>
  <testcase name="PWASearch" time="59.817" classname="com.zee5.PWAScripts.PWAMainScript">
    <error type="org.openqa.selenium.TimeoutException" message="Expected condition failed: waiting for presence of element located by: By.xpath: (//h3[@class=&#039;cardTitle&#039;]/span)[1] (tried for 0 second(s) with 500 milliseconds interval)">
      <![CDATA[org.openqa.selenium.TimeoutException: Expected condition failed: waiting for presence of element located by: By.xpath: (//h3[@class='cardTitle']/span)[1] (tried for 0 second(s) with 500 milliseconds interval)
	at org.openqa.selenium.support.ui.WebDriverWait.timeoutException(WebDriverWait.java:95)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:272)
	at com.utility.Utilities.findElement(Utilities.java:118)
	at com.utility.Utilities.getText(Utilities.java:282)
	at com.business.zee.Zee5PWABusinessLogic.fetchLiveContent(Zee5PWABusinessLogic.java:2412)
	at com.zee5.PWAScripts.PWAMainScript.PWASearch(PWAMainScript.java:108)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: org.openqa.selenium.NoSuchElementException: no such element (An element could not be located on the page using the given search parameters (XPATH='(//h3[@class='cardTitle']/span)[1]'))  (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:17:03'
System info: host: 'LAPTOP-NRJMSC7T', ip: '*************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_231'
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.google.android.apps.chr..., appBuildVersion: , appPackage: com.android.chrome, appReleaseVersion: , appiumVersion: 1.8.0, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, browserName: Chrome, commandTimeouts: 120000, compressXml: true, desired: {appActivity: com.google.android.apps.chr..., appPackage: com.android.chrome, autoAcceptAlerts: true, automationName: uiautomator2, browserName: Chrome, compressXml: true, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: UNKNOWN, device.majorVersion: 8, device.manufacture: vivo, device.model: vivo 1820, device.name: vivo 1820, device.os: Android, device.screenSize: 720x1520, device.serialNumber: OVS87SCAIRW8B6LN, device.version: 8.1.0, deviceName: Android, deviceUDID: OVS87SCAIRW8B6LN, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudioen..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 05/04/20 09:28 PM, udid: OVS87SCAIRW8B6LN, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: 4e15a136-631f-4ddd-928e-543a84860253
*** Element info: {Using=xpath, value=(//h3[@class='cardTitle']/span)[1]}
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.openqa.selenium.remote.ErrorHandler.createThrowable(ErrorHandler.java:214)
	at org.openqa.selenium.remote.ErrorHandler.throwIfResponseFailed(ErrorHandler.java:166)
	at org.openqa.selenium.remote.http.JsonHttpResponseCodec.reconstructValue(JsonHttpResponseCodec.java:40)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:80)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:44)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.support.ui.ExpectedConditions$6.apply(ExpectedConditions.java:182)
	at org.openqa.selenium.support.ui.ExpectedConditions$6.apply(ExpectedConditions.java:179)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:249)
	... 28 more
]]>
    </error>
  </testcase> <!-- PWASearch -->
  <testcase name="PWACarousel" time="1018.125" classname="com.zee5.PWAScripts.PWAMainScript"/>
  <testcase name="PWAUICheck" time="190.847" classname="com.zee5.PWAScripts.PWAMainScript"/>
  <testcase name="PWASubscription" time="154.257" classname="com.zee5.PWAScripts.PWAMainScript"/>
  <testcase name="PWALandingScreen" time="63.711" classname="com.zee5.PWAScripts.PWAMainScript">
    <error type="java.lang.IllegalArgumentException" message="Cannot invoke method getAt() on null object">
      <![CDATA[java.lang.IllegalArgumentException: Cannot invoke method getAt() on null object
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:77)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:102)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:57)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:182)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:194)
	at com.jayway.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:57)
	at com.jayway.restassured.internal.path.json.JSONAssertion$getAsJsonObject.callCurrent(Unknown Source)
	at com.jayway.restassured.internal.path.json.JSONAssertion.getResult(JSONAssertion.groovy:32)
	at com.jayway.restassured.path.json.JsonPath.get(JsonPath.java:202)
	at com.jayway.restassured.path.json.JsonPath.getString(JsonPath.java:351)
	at com.business.zee.Zee5PWABusinessLogic.HomepageTrayTitleAndContentValidationWithApiData(Zee5PWABusinessLogic.java:1020)
	at com.business.zee.Zee5PWABusinessLogic.ValidatingLandingPages(Zee5PWABusinessLogic.java:994)
	at com.zee5.PWAScripts.PWAMainScript.PWALandingScreen(PWAMainScript.java:123)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: java.lang.NullPointerException: Cannot invoke method getAt() on null object
	at org.codehaus.groovy.runtime.NullObject.invokeMethod(NullObject.java:88)
	at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:45)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:45)
	at org.codehaus.groovy.runtime.callsite.NullCallSite.call(NullCallSite.java:32)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:45)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:108)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
	at Script1.run(Script1.groovy:1)
	at groovy.lang.GroovyShell.evaluate(GroovyShell.java:570)
	at groovy.lang.GroovyShell.evaluate(GroovyShell.java:608)
	at groovy.lang.GroovyShell.evaluate(GroovyShell.java:579)
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrap.invoke(PogoMetaMethodSite.java:187)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.call(PogoMetaMethodSite.java:68)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
	at com.jayway.restassured.internal.path.json.JSONAssertion.eval(JSONAssertion.groovy:80)
	at com.jayway.restassured.internal.path.json.JSONAssertion.this$2$eval(JSONAssertion.groovy)
	at com.jayway.restassured.internal.path.json.JSONAssertion$this$2$eval$0.callCurrent(Unknown Source)
	at com.jayway.restassured.internal.path.json.JSONAssertion.getAsJsonObject(JSONAssertion.groovy:50)
	... 31 more
]]>
    </error>
  </testcase> <!-- PWALandingScreen -->
  <testcase name="PWAPlayer" time="466.576" classname="com.zee5.PWAScripts.PWAMainScript">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
	at com.metadata.getResponseUpNextRail.getMediaContentName(getResponseUpNextRail.java:24)
	at com.business.zee.Zee5PWABusinessLogic.UpnextRail(Zee5PWABusinessLogic.java:2716)
	at com.zee5.PWAScripts.PWAMainScript.PWAPlayer(PWAMainScript.java:48)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- PWAPlayer -->
</testsuite> <!-- com.zee5.PWAScripts.PWAMainScript -->
