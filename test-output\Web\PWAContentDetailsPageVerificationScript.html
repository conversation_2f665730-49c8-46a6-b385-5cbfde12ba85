<html>
<head>
<title>TestNG:  PWAContentDetailsPageVerificationScript</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>PWAContentDetailsPageVerificationScript</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/6/6</td>
</tr><tr>
<td>Started on:</td><td>Mon Apr 06 13:03:25 IST 2020</td>
</tr>
<tr><td>Total time:</td><td>485 seconds (485921 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.tearDown()'><b>tearDown</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.tearDown(Zee5PWABusinessLogic.java:160)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.tearDown(PWAContentDetailsPageVerificationScript.java:89)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1255445356", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1255445356'><pre>java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.tearDown(Zee5PWABusinessLogic.java:160)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.tearDown(PWAContentDetailsPageVerificationScript.java:89)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeConfigurationMethod(Invoker.java:514)
	at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:215)
	at org.testng.internal.Invoker.invokeConfigurations(Invoker.java:142)
	at org.testng.TestRunner.afterRun(TestRunner.java:1012)
	at org.testng.TestRunner.run(TestRunner.java:636)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>null</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen()'><b>verifyConsumptionsScreen</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript<br>Parameters: chrome, https://newpwa.zee5.com/, Guest, 1619</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1468)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen(PWAContentDetailsPageVerificationScript.java:32)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace848958019", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace848958019'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1468)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen(PWAContentDetailsPageVerificationScript.java:32)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:967)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption()'><b>verifyMetaDataInDetailsAndConsumption</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetaDataInDetailsAndConsumption(Zee5PWABusinessLogic.java:1633)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption(PWAContentDetailsPageVerificationScript.java:78)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1872088401", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1872088401'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetaDataInDetailsAndConsumption(Zee5PWABusinessLogic.java:1633)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption(PWAContentDetailsPageVerificationScript.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:967)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent()'><b>verifyNoSubscriptionPopupForFreeContent</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript<br>Parameters: Guest, 1619</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyNoSubscriptionPopupForFreeContent(Zee5PWABusinessLogic.java:1550)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent(PWAContentDetailsPageVerificationScript.java:61)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1769598893", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1769598893'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyNoSubscriptionPopupForFreeContent(Zee5PWABusinessLogic.java:1550)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent(PWAContentDetailsPageVerificationScript.java:61)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:967)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails()'><b>verifyShareInShowDetails</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyShareInShowDetails(Zee5PWABusinessLogic.java:1704)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails(PWAContentDetailsPageVerificationScript.java:84)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace855501888", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace855501888'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyShareInShowDetails(Zee5PWABusinessLogic.java:1704)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails(PWAContentDetailsPageVerificationScript.java:84)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:967)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent()'><b>verifySubscriptionPopupForPremiumContent</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript<br>Parameters: Guest, 1619</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifySubscriptionPopupForPremiumContent(Zee5PWABusinessLogic.java:1589)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent(PWAContentDetailsPageVerificationScript.java:70)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace379430898", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace379430898'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifySubscriptionPopupForPremiumContent(Zee5PWABusinessLogic.java:1589)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent(PWAContentDetailsPageVerificationScript.java:70)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:967)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA()'><b>verifyWatchLatestEpisodeCTA</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyWatchLatestEpisodeCTA(Zee5PWABusinessLogic.java:1508)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA(PWAContentDetailsPageVerificationScript.java:51)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1048128739", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1048128739'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyWatchLatestEpisodeCTA(Zee5PWABusinessLogic.java:1508)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA(PWAContentDetailsPageVerificationScript.java:51)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.retryFailed(Invoker.java:967)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen()'><b>verifyConsumptionsScreen</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript<br>Parameters: chrome, https://newpwa.zee5.com/, Guest, 1619</td>
<td><div><pre>java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1482)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen(PWAContentDetailsPageVerificationScript.java:32)
... Removed 24 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1340057206", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1340057206'><pre>java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1482)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen(PWAContentDetailsPageVerificationScript.java:32)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>132</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption()'><b>verifyMetaDataInDetailsAndConsumption</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetaDataInDetailsAndConsumption(Zee5PWABusinessLogic.java:1633)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption(PWAContentDetailsPageVerificationScript.java:78)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace845119401", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace845119401'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetaDataInDetailsAndConsumption(Zee5PWABusinessLogic.java:1633)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption(PWAContentDetailsPageVerificationScript.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent()'><b>verifyNoSubscriptionPopupForFreeContent</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript<br>Parameters: Guest, 1619</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyNoSubscriptionPopupForFreeContent(Zee5PWABusinessLogic.java:1550)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent(PWAContentDetailsPageVerificationScript.java:61)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace237484673", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace237484673'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyNoSubscriptionPopupForFreeContent(Zee5PWABusinessLogic.java:1550)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent(PWAContentDetailsPageVerificationScript.java:61)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails()'><b>verifyShareInShowDetails</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyShareInShowDetails(Zee5PWABusinessLogic.java:1704)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails(PWAContentDetailsPageVerificationScript.java:84)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1400582895", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1400582895'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyShareInShowDetails(Zee5PWABusinessLogic.java:1704)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails(PWAContentDetailsPageVerificationScript.java:84)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent()'><b>verifySubscriptionPopupForPremiumContent</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript<br>Parameters: Guest, 1619</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifySubscriptionPopupForPremiumContent(Zee5PWABusinessLogic.java:1589)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent(PWAContentDetailsPageVerificationScript.java:70)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace708926121", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace708926121'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifySubscriptionPopupForPremiumContent(Zee5PWABusinessLogic.java:1589)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent(PWAContentDetailsPageVerificationScript.java:70)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
<tr>
<td title='com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA()'><b>verifyWatchLatestEpisodeCTA</b><br>Test class: com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyWatchLatestEpisodeCTA(Zee5PWABusinessLogic.java:1508)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA(PWAContentDetailsPageVerificationScript.java:51)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace681747431", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace681747431'><pre>java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyWatchLatestEpisodeCTA(Zee5PWABusinessLogic.java:1508)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA(PWAContentDetailsPageVerificationScript.java:51)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>30</td>
<td>com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript@dc24521</td></tr>
</table><p>
</body>
</html>