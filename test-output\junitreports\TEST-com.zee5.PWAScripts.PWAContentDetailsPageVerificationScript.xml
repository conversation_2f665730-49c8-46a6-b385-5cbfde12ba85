<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript" tests="6" failures="0" timestamp="9 Apr 2020 06:51:26 GMT" time="964.693" errors="1">
  <testcase name="verifyWatchLatestEpisodeCTA" time="46.933" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript"/>
  <testcase name="verifySubscriptionPopupForPremiumContent" time="63.154" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript"/>
  <testcase name="verifyConsumptionsScreen" time="670.802" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1395)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen(PWAContentDetailsPageVerificationScript.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- verifyConsumptionsScreen -->
  <testcase name="verifyMetaDataInDetailsAndConsumption" time="48.080" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript"/>
  <testcase name="verifyNoSubscriptionPopupForFreeContent" time="70.305" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript"/>
  <testcase name="verifyShareInShowDetails" time="65.419" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript"/>
</testsuite> <!-- com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript -->
