<html><head><title>testng.xml for GCash_Suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;name="GCash_Suite"&nbsp;guice-stage="DEVELOPMENT"&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="APIUrl"&nbsp;value="https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="GSaveValidPhoneNumber"&nbsp;value="9010000105"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="GSaveValidOTP"&nbsp;value="000000"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="validphonenumber"&nbsp;value="9664087954"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="repaymentAMT"&nbsp;value="1.00"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="runMode"&nbsp;value="Suites"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="InvalidphoneNumber"&nbsp;value="9999999999"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="browserType"&nbsp;value="chrome"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="testExecutionKey"&nbsp;value="TC-21175"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="GGivesLoginValidOTP"&nbsp;value="000000"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="runModule"&nbsp;value="Suite"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="userType"&nbsp;value="Guest"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="amtPay"&nbsp;value="1.00"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="GGivesLoginInValidOTP"&nbsp;value="000001"/&gt;
<br/>&nbsp;&nbsp;&lt;listeners&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;listener&nbsp;class-name="com.extent.ExtentReporter"/&gt;
<br/>&nbsp;&nbsp;&lt;/listeners&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="5"&nbsp;name="GCASH"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.GCash_GGivesScripts.GCASHScripts"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.GCash_GGivesScripts.TokenGCASH"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;GCASH&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;GCash_Suite&nbsp;--&gt;
<br/></tt></body></html>