<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="AndroidPWASanityScript" tests="2" failures="1" timestamp="5 Jun 2020 12:12:34 GMT" time="324.559" errors="0">
  <testcase name="Login" time="35.208" classname="com.zee5.PWASanityScripts.AndroidPWASanityScript"/>
  <testcase name="PWAMovies" time="177.393" classname="com.zee5.PWASanityScripts.AndroidPWASanityScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementClose But<PERSON>  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementClose Button  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:235)
	at com.business.zee.Zee5PWASanityAndroidBusinessLogic.watchTrailerButtonFunctionality(Zee5PWASanityAndroidBusinessLogic.java:9145)
	at com.business.zee.Zee5PWASanityAndroidBusinessLogic.landingPagesTrailerAndPopUpValidation(Zee5PWASanityAndroidBusinessLogic.java:9111)
	at com.business.zee.Zee5PWASanityAndroidBusinessLogic.Moviepage(Zee5PWASanityAndroidBusinessLogic.java:7958)
	at com.zee5.PWASanityScripts.AndroidPWASanityScript.PWAMovies(AndroidPWASanityScript.java:58)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- PWAMovies -->
</testsuite> <!-- AndroidPWASanityScript -->
