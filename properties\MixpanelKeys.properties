Boolean = isTalamoos, Ad isEmpty, Subtitles, Search Success, Current Subscription, Success, isPWA

Integer = Pack Duration, Load Time, Content Duration, Episode No, Vertical Index, Horizontal Index, NUTS Value

Float = Cost

List = Old Content Language, New Content Language

String = Unique ID \
, Session ID, User Type, Next Expiring Pack, Next Pack Expiry Date, Registring Country, Gender, Latest Subscription Pack, Latest Subscription Pack Expiry, Age, Parent Control Setting, Platform Name, dekey, Method, Social Network, Source, Failure Reason, Payment Method, Partner Name, Pack Selected, Transaction Currency, Billing Country, Billing State, Promo Cod, Payment Gateway, Results Returned, Content Name, Content ID, Content Type, Genre, Characters, Series, Sharing Platform, Top Category, Content Specification, Channel Name, Content Original Language, Audio Language, Subtitle Language\
, Tab Name \
, Carousal Name \
, Direction \
, Page Name \
, Vertical Index \
, Horizontal Index \
, Pop Up Name \
, Pop Up Type \
, Popup Group \
, Search Type \
, Search Query \
, Old Video Streaming Quality Setting \
, New Video Streaming Quality Setting \
, Old Autoplay Setting \
, New Autoplay Setting \
, Old App Language \
, New App Language \
, Setting Changed \
, Old Setting Value \
, New Setting Value \
, Ad Campaign ID \
, Ad Creative ID \
, Ad id \
, Ad Location \
, Ad Size \
, Talamoos origin \
, Talamoos modelName \
, Talamoos Click ID \
, Experiment Name \
, Variant Name \
, Landing Page \
, hasEduauraa \
, isEduauraa

home = homepage
movies = movies
shows = tvshows
news = 626
kids = 3673
music = 2707
zee5original = zeeoriginals
trendingSearch	= conviva-trending
play = 4603
club = 5851
premium = premiumcontents
videos = videos


kids_details = kids_movie_detail
music_details = music_videos_detail
live_details = channel_detail
movies_details = movie_detail
zee5originals_details = zee5originals_detail
tvshows_details = show_detail
episode_details = episode_detail
video_details = video_detail

kids_view = kids_landing
music_view = music_videos_landing
live_view = channel_detail
movies_view = movie_landing
zee5originals_view = zee_originals_view all
tvshows_view =  tv_shows_view_all
video_view = video_landing
home_view = home
news_view= news_landing
premium_view=premium
zee_plex_view = zee_plex_landing

tv_home = manualcol_2081341971
tv_movies = manualcoll_160025695
tv_shows = manualcol_1274139062
tv_news = 626
tv_premium = manualcoll_317983822
tv_videos = manualcol_1852704674


tamil = ta
hindi = hi
bengali = bn
kannada = kn
malayalam = ml
telugu = te


#Subscription Details
Sub_Next_Expiring_Pack = [0-11-1481_Premium Pack_SVOD]

Sub_Next_Pack_Expiry_Date = 2021-06-10T05:29:59

Sub_Latest_Subscription_Pack = 0-11-1481_Premium Pack_SVOD

Sub_Latest_Subscription_Pack_Expiry = 2021-06-10T05:29:59

Sub_Free_Trial_Expiry_Date = N/A

Sub_Free_Trial_Package = N/A

Sub_Pack_Duration = 90

Sub_hasEduauraa = true

Sub_HasRental = false

#NonSubscribedUser
NonSub_Next_Expiring_Pack = N/A

NonSub_Next_Pack_Expiry_Date = N/A

NonSub_Latest_Subscription_Pack = N/A

NonSub_Latest_Subscription_Pack_Expiry = N/A

NonSub_Free_Trial_Expiry_Date = N/A

NonSub_Free_Trial_Package = N/A

NonSub_Pack_Duration = N/A

NonSub_hasEduauraa = true

NonSub_HasRental = false
