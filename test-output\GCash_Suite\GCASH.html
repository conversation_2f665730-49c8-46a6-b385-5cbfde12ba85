<html>
<head>
<title>TestNG:  GCASH</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>GCASH</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/0/8</td>
</tr><tr>
<td>Started on:</td><td>Mon Sep 19 21:48:51 IST 2022</td>
</tr>
<tr><td>Total time:</td><td>12 seconds (12832 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.GCash_GGivesScripts.GCASHScripts.Before()'><b>Before</b><br>Test class: com.GCash_GGivesScripts.GCASHScripts</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace628131102", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace628131102'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>2</td>
<td>com.GCash_GGivesScripts.GCASHScripts@553d2579</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.GCash_GGivesScripts.GCASHScripts.Allowpopup()'><b>Allowpopup</b><br>Test class: com.GCash_GGivesScripts.GCASHScripts</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1885769953", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1885769953'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.GCASHScripts@553d2579</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.EmptyClientId_TokenGCash()'><b>EmptyClientId_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace975853302", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace975853302'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.EmptyClientSecret_TokenGCash()'><b>EmptyClientSecret_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1787667909", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1787667909'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.EmptyGrantType_TokenGCash()'><b>EmptyGrantType_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace370034120", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace370034120'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.InvalidClientId_TokenGCash()'><b>InvalidClientId_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2141044241", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2141044241'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.InvalidClientSecret_TokenGCash()'><b>InvalidClientSecret_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace401132274", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace401132274'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.InvalidGrantType_TokenGCash()'><b>InvalidGrantType_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace273479239", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace273479239'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.TokenGCash_200()'><b>TokenGCash_200</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
... Removed 22 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1955119491", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1955119491'><pre>org.testng.SkipException: PlatForm not matched...
	at com.driverInstance.Drivertools.&lt;init&gt;(Drivertools.java:352)
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:36)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:51)
	at com.business.gCASH.GCASHBusinessLogic.&lt;init&gt;(GCASHBusinessLogic.java:40)
	at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:20)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:63)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:348)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:302)
	at org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:619)
	at org.testng.TestRunner.beforeRun(TestRunner.java:609)
	at org.testng.TestRunner.run(TestRunner.java:580)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@2a8f6e6</td></tr>
</table><p>
</body>
</html>