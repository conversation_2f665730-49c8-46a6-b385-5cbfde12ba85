

<!DOCTYPE html>
<html>
<head>
	<meta charset='UTF-8' /> 
	<meta name='description' content='' />
	<meta name='robots' content='noodp, noydir' />
	<meta name='viewport' content='width=device-width, initial-scale=1' />
	<meta id="timeStampFormat" name="timeStampFormat" content='MMM d, yyyy hh:mm:ss a'/>
	
	<link href='https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,600' rel='stylesheet' type='text/css' />
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
	<link href='https://cdn.rawgit.com/extent-framework/extent-github-cdn/b65cd69/v3html/css/extent.css' type='text/css' rel='stylesheet' />
	
	<title>Automation Reports</title>

	<style type='text/css'>
		/* json-tree */
		.jstBracket,.jstComma,.jstValue{white-space:pre-wrap}.jstValue{font-size:10px;font-weight:400;font-family:"Lucida Console",Monaco,monospace}.jstProperty{color:#666;word-wrap:break-word}.jstBool{color:#2525CC}.jstNum{color:#D036D0}.jstNull{color:gray}.jstStr{color:#2DB669}.jstFold:after{content:' -';cursor:pointer}.jstExpand{white-space:normal}.jstExpand:after{content:' +';cursor:pointer}.jstFolded{white-space:normal!important}.jstHiddenBlock{display:none}
			
            
                 .report-name { padding-left: 10px; } .report-name > img { float: left;height: 90%;margin-left: 90px;margin-top: 2px;width: 50px; }
            
		
	</style>
	
	<script type="text/javascript">
		/*! json-tree - v0.2.2 - 2017-09-25, MIT LICENSE */
		var JSONTree=function(){var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"},t=0,r=0;this.create=function(n,t){return r+=1,N(u(n,0,!1),{class:"jstValue"})};var e=function(t){return t.replace(/[&<>'"]/g,function(t){return n[t]})},s=function(){return r+"_"+t++},u=function(n,t,r){if(null===n)return f(r?t:0);switch(typeof n){case"boolean":return l(n,r?t:0);case"number":return i(n,r?t:0);case"string":return o(n,r?t:0);default:return n instanceof Array?a(n,t,r):c(n,t,r)}},c=function(n,t,r){var e=s(),u=Object.keys(n).map(function(r){return j(r,n[r],t+1,!0)}).join(m()),c=[g("{",r?t:0,e),N(u,{id:e}),p("}",t)].join("\n");return N(c,{})},a=function(n,t,r){var e=s(),c=n.map(function(n){return u(n,t+1,!0)}).join(m());return[g("[",r?t:0,e),N(c,{id:e}),p("]",t)].join("\n")},o=function(n,t){var r=e(JSON.stringify(n));return N(v(r,t),{class:"jstStr"})},i=function(n,t){return N(v(n,t),{class:"jstNum"})},l=function(n,t){return N(v(n,t),{class:"jstBool"})},f=function(n){return N(v("null",n),{class:"jstNull"})},j=function(n,t,r){var s=v(e(JSON.stringify(n))+": ",r),c=N(u(t,r,!1),{});return N(s+c,{class:"jstProperty"})},m=function(){return N(",\n",{class:"jstComma"})},N=function(n,t){return d("span",t,n)},d=function(n,t,r){return"<"+n+Object.keys(t).map(function(n){return" "+n+'="'+t[n]+'"'}).join("")+">"+r+"</"+n+">"},g=function(n,t,r){return N(v(n,t),{class:"jstBracket"})+N("",{class:"jstFold",onclick:"JSONTree.toggle('"+r+"')"})};this.toggle=function(n){var t=document.getElementById(n),r=t.parentNode,e=t.previousElementSibling;""===t.className?(t.className="jstHiddenBlock",r.className="jstFolded",e.className="jstExpand"):(t.className="",r.className="",e.className="jstFold")};var p=function(n,t){return N(v(n,t),{})},v=function(n,t){return Array(2*t+1).join(" ")+n};return this}();
	</script>
</head>
	<body class='extent standard default hide-overflow '>
		<div id='theme-selector' alt='Click to toggle theme. To enable by default, use theme configuration.' title='Click to toggle theme. To enable by default, use theme configuration.'>
			<span><i class='material-icons'>desktop_windows</i></span>
		</div>
<nav>
	<div class="nav-wrapper">
		<a href="#!" class="brand-logo black"><img src="https://cdn.rawgit.com/extent-framework/extent-github-cdn/d74480e/commons/img/logo.png"></a>
		<!-- slideout menu -->
		<ul id='slide-out' class='side-nav fixed hide-on-med-and-down'>
			<li class='waves-effect active'><a href='#!' view='test-view' onclick="configureView(0);chartsView('test');"><i class='material-icons'>dashboard</i></a></li>
						<li class='waves-effect'><a href='#!' onclick="configureView(-1);chartsView('dashboard');" view='dashboard-view'><i class='material-icons'>track_changes</i></a></li>
		</ul>
		<!-- report name -->
		<span class='report-name'>GCASH API Automation Demo</span>
		<!-- report headline -->
		<span class='report-headline'></span>
		<!-- nav-right -->
		<ul id='nav-mobile' class='right hide-on-med-and-down nav-right'>
			<a href='#!'>
			<span class='label blue darken-3 suite-start-time'>Sep 21, 2022 12:03:31 AM</span>
			</a>
		</ul>
	</div>
</nav>		<!-- container -->
		<div class='container'>
<div id='test-view' class='view'>
	<section id='controls'>
		<div class='controls grey lighten-4'>
			<!-- test toggle -->
			<div class='chip transparent'>
				<a class='dropdown-button tests-toggle' data-activates='tests-toggle' data-constrainwidth='true' data-beloworigin='true' data-hover='true' href='#'>
				<i class='material-icons'>warning</i> Status
				</a>
				<ul id='tests-toggle' class='dropdown-content'>
										<li status='pass'><a href='#!'>Pass <i class='material-icons green-text'>check_circle</i></a></li>
					<li class='divider'></li>
					<li status='clear' clear='true'><a href='#!'>Clear Filters <i class='material-icons'>clear</i></a></li>
				</ul>
			</div>
			<!-- test toggle -->
			<!-- category toggle -->
			<!-- category toggle -->
			<!-- clear filters -->
			<div class='chip transparent hide'>
				<a class='' id='clear-filters' alt='Clear Filters' title='Clear Filters'>
				<i class='material-icons'>close</i> Clear
				</a>
			</div>
			<!-- clear filters -->
			<!-- enable dashboard -->
			<div id='toggle-test-view-charts' class='chip transparent'>
				<a class='pink-text' id='enable-dashboard' alt='Enable Dashboard' title='Enable Dashboard'>
				<i class='material-icons'>track_changes</i> Dashboard
				</a>
			</div>
			<!-- enable dashboard -->
			<!-- search -->
			<div class='chip transparent' alt='Search Tests' title='Search Tests'>
				<a href="#" class='search-div'>
				<i class='material-icons'>search</i> Search
				</a>
				<div class='input-field left hide'>
					<input id='search-tests' type='text' class='validate browser-default' placeholder='Search Tests...'>
				</div>
			</div>
			<!-- search -->
		</div>
	</section>
<div id='test-view-charts' class='subview-full'>
    <div id='charts-row' class='row nm-v nm-h'>
        <div class='col s12 m12 l12 np-h'>
            <div class='card-panel nm-v'>
                <div class='left panel-name'>Tests</div>
                <div class='chart-box' style="max-height:94px;">
                    <canvas id='parent-analysis' width='90' height='70'></canvas>
                </div>
                <div class='block text-small'>
                    <span class='tooltipped' data-position='top' data-tooltip='100%'><span class='strong'>7</span> test(s) passed</span>
                </div>
                <div class='block text-small'>
                    <span class='strong tooltipped' data-position='top' data-tooltip='0%'>0</span> test(s) failed, <span class='strong tooltipped' data-position='top' data-tooltip='0%'>0</span> others
                </div>
            </div>
        </div>
    </div>
    <div id="timeline-chart" class="row nm-v nm-h">
        <div class="col s12 m12 l12 np-h">
            <div class="card-panel">
                <div class='left panel-name'>Timeline (seconds)</div>
                <div class="chart-box" style="width:98%;max-height:145px;">
                    <canvas id="timeline" height="120"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>	<div class='subview-left left'>
		<div class='view-summary'>
			<ul id='test-collection' class='test-collection'>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='1'>
					<div class='test-heading'>
						<span class='test-name'>GCashToken</span>
						<span class='test-time'>Sep 21, 2022 12:04:09 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:09 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:09 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='2'>
					<div class='test-heading'>
						<span class='test-name'>GCashClientId</span>
						<span class='test-time'>Sep 21, 2022 12:04:10 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='3'>
					<div class='test-heading'>
						<span class='test-name'>GCashEmptyId</span>
						<span class='test-time'>Sep 21, 2022 12:04:10 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='4'>
					<div class='test-heading'>
						<span class='test-name'>GCashSecretId</span>
						<span class='test-time'>Sep 21, 2022 12:04:10 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='5'>
					<div class='test-heading'>
						<span class='test-name'>GCashInvalidSecret</span>
						<span class='test-time'>Sep 21, 2022 12:04:10 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='6'>
					<div class='test-heading'>
						<span class='test-name'>GCashGrant</span>
						<span class='test-time'>Sep 21, 2022 12:04:10 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
				<li class='test displayed active  pass' status='pass' bdd='false' test-id='7'>
					<div class='test-heading'>
						<span class='test-name'>GCashEmptyGrant</span>
						<span class='test-time'>Sep 21, 2022 12:04:10 AM</span>
						<span class='test-status right pass'>pass</span>
					</div>
					<div class='test-content hide'>
<div class='test-time-info'>
	<span class='label start-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label end-time'>Sep 21, 2022 12:04:10 AM</span>
	<span class='label time-taken grey lighten-1 white-text'>0h 0m 0s+0ms</span>
</div>
<div class='test-desc'><h5> ENV : <a href="Native App" onclick='return false;'">Native App</a></h5> </div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<!-- subview left -->
	<div class='subview-right left'>
		<div class='view-summary'>
			<div id='step-filters' class="right sr-filters">
				<a class="btn-floating waves-effect waves-light green" status="pass" alt="pass" title="pass"><i class="material-icons">check_circle</i></a>
				<a class="btn-floating waves-effect waves-light red" status="fail" alt="fail" title="fail"><i class="material-icons">cancel</i></a>
				<a class="btn-floating waves-effect waves-light red darken-4" status="fatal" alt="fatal" title="fatal"><i class="material-icons">cancel</i></a>
				<a class="btn-floating waves-effect waves-light pink text-lighten-1" status="error" alt="error" title="error"><i class="material-icons">error</i></a>
				<a class="btn-floating waves-effect waves-light orange" alt="warning" status="warning" title="warning"><i class="material-icons">warning</i></a>
				<a class="btn-floating waves-effect waves-light teal" status="skip" alt="skip" title="skip"><i class="material-icons">redo</i></a>
				<a class="btn-floating waves-effect waves-light grey" status="clear" alt="Clear filters" title="Clear filters"><i class="material-icons">clear</i></a>
			</div>
			<h5 class='test-name'></h5>
		</div>
	</div>
	<!-- subview right -->
</div>
<!-- test view --><!-- category view --><!-- exception view --><div id='dashboard-view' class='view hide'>
	<div class='card-panel transparent np-v'>
		<h5>Dashboard</h5>

		<div class='row'>
			<div class='col s2'>
				<div class='card-panel r'>
					Tests
					<div class='panel-lead'>7</div>
				</div>
			</div>
			<div class='col s2'>
				<div class='card-panel r'>
					Steps
					<div class='panel-lead'>0</div>
				</div>
			</div>
			<div class='col s2'>
				<div class='card-panel r'>
					Start
					<div class='panel-lead'>Sep 21, 2022 12:03:31 AM</div>
				</div>
			</div>
			<div class='col s2'>
				<div class='card-panel r'>
			 		End
			 		<div class='panel-lead'>Sep 21, 2022 12:04:10 AM</div>
				</div>
			</div>
			<div class='col s2'>
				<div class='card-panel r'>
					Time Taken
					<div class='panel-lead'>0h 0m 38s+464ms</div>
				</div>
			</div>
			<div class='col s4'>
				<div class='card-panel dashboard-environment'>
					<span class='right label cyan white-text'>Environment</span><p>&nbsp;</p>
					
					<table>
						<tr>
							<th>Name</th>
							<th>Value</th>
						</tr>
							<tr>
								<td>Device Info </td>
								<td>Device Name - samsung Version - 11</td>
							</tr>
							<tr>
								<td>App version : </td>
								<td> </td>
							</tr>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- dashboard view -->
<!-- testrunner-logs view -->		</div>
		<!-- container -->
		<script>
			var statusGroup = {
                parentCount: 7,
				passParent: 7,
				failParent: 0,
				fatalParent: 0,
				errorParent: 0,
				warningParent: 0,
				skipParent: 0,
				exceptionsParent: 0,
				childCount: 0,
				passChild: 0,
				failChild: 0,
				fatalChild: 0,
				errorChild: 0,
				warningChild: 0,
				skipChild: 0,
				infoChild: 0,
				debugChild: 0,
				exceptionsChild: 0,
				grandChildCount: 0,
				passGrandChild: 0,
				failGrandChild: 0,
				fatalGrandChild: 0,
				errorGrandChild: 0,
				warningGrandChild: 0,
				skipGrandChild: 0,
				infoGrandChild: 0,
				debugGrandChild: 0,
				exceptionsGrandChild: 0,
			};
		</script>
		<script>
			var timeline = {
			        "GCashToken":0,"GCashClientId":0,"GCashEmptyId":0,"GCashSecretId":0,"GCashInvalidSecret":0,"GCashGrant":0,"GCashEmptyGrant":0
			};
		</script>
		<script src='https://cdn.rawgit.com/extent-framework/extent-github-cdn/b65cd69/v3html/js/extent.js' type='text/javascript'></script>
		<script type='text/javascript'>
			
            
            
                $(document).ready(function() {
                });
            
		
		</script>
	</body>
</html>