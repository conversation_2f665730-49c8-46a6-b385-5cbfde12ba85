<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="LAPTOP-7AIFL2R7" failures="1" tests="1" name="vootTV" time="896.076" errors="0" timestamp="2022-07-14T10:00:26 IST">
  <testcase classname="com.VootTvScripts.TvVootScripts" name="premiumPage" time="212.993">
    <failure type="org.openqa.selenium.NoSuchElementException" message="An element could not be located on the page using the given search parameters.
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: &amp;apos;3.141.59&amp;apos;, revision: &amp;apos;e82be7d358&amp;apos;, time: &amp;apos;2018-11-14T08:17:03&amp;apos;
System info: host: &amp;apos;LAPTOP-7AIFL2R7&amp;apos;, ip: &amp;apos;************&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;17.0.1&amp;apos;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, databaseEnabled: false, desired: {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: checkTv, fullReset: false, newCommandTimeout: 300, platformName: android}, deviceApiLevel: 30, deviceManufacturer: Google, deviceModel: AOSP TV on x86, deviceName: emulator-5554, deviceScreenDensity: 213, deviceScreenSize: 1280x720, deviceUDID: emulator-5554, fullReset: false, javascriptEnabled: true, locationContextEnabled: false, networkConnectionEnabled: true, newCommandTimeout: 300, pixelRatio: 1.3312501, platform: LINUX, platformName: Android, platformVersion: 11, statBarHeight: 0, takesScreenshot: true, viewportRect: {height: 720, left: 0, top: 0, width: 1280}, warnings: {}, webStorageEnabled: false}
Session ID: 75ffb4ee-4e1f-4340-858b-55dce5b9a479
*** Element info: {Using=xpath, value=//*[@text=&amp;apos;Halo&amp;apos;]}">
      <![CDATA[org.openqa.selenium.NoSuchElementException: An element could not be located on the page using the given search parameters.
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:17:03'
System info: host: 'LAPTOP-7AIFL2R7', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.1'
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, databaseEnabled: false, desired: {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: checkTv, fullReset: false, newCommandTimeout: 300, platformName: android}, deviceApiLevel: 30, deviceManufacturer: Google, deviceModel: AOSP TV on x86, deviceName: emulator-5554, deviceScreenDensity: 213, deviceScreenSize: 1280x720, deviceUDID: emulator-5554, fullReset: false, javascriptEnabled: true, locationContextEnabled: false, networkConnectionEnabled: true, newCommandTimeout: 300, pixelRatio: 1.3312501, platform: LINUX, platformName: Android, platformVersion: 11, statBarHeight: 0, takesScreenshot: true, viewportRect: {height: 720, left: 0, top: 0, width: 1280}, warnings: {}, webStorageEnabled: false}
Session ID: 75ffb4ee-4e1f-4340-858b-55dce5b9a479
*** Element info: {Using=xpath, value=//*[@text='Halo']}
at org.openqa.selenium.remote.http.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:187)
at org.openqa.selenium.remote.http.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:122)
at org.openqa.selenium.remote.http.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:49)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
at com.utility.Utilities.TVgetText(Utilities.java:2157)
at com.business.voot.VootTvBusinessLogic.premiunPageValidation(VootTvBusinessLogic.java:525)
at com.VootTvScripts.TvVootScripts.premiumPage(TvVootScripts.java:30)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 32 stack frames]]>
    </failure>
  </testcase> <!-- premiumPage -->
</testsuite> <!-- vootTV -->
