<html>
<head>
<title>TestNG:  TvZee5TvperformanceScripts</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>TvZee5TvperformanceScripts</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/5/0</td>
</tr><tr>
<td>Started on:</td><td>Mon Dec 06 13:33:52 GMT 2021</td>
</tr>
<tr><td>Total time:</td><td>563 seconds (563450 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.appLaunchPerformance()'><b>appLaunchPerformance</b><br>Test class: com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts</td>
<td><div><pre>java.lang.NullPointerException: Cannot invoke &quot;String.trim()&quot; because the return value of &quot;java.io.BufferedReader.readLine()&quot; is null
	at com.business.zee.Zee5TvBusinessLogic.Memory_UsagePerformance(Zee5TvBusinessLogic.java:11400)
	at com.business.zee.Zee5TvBusinessLogic.appLaunch(Zee5TvBusinessLogic.java:11669)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.appLaunchPerformance(TvZee5TvperformanceScripts.java:20)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace235532726", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace235532726'><pre>java.lang.NullPointerException: Cannot invoke &quot;String.trim()&quot; because the return value of &quot;java.io.BufferedReader.readLine()&quot; is null
	at com.business.zee.Zee5TvBusinessLogic.Memory_UsagePerformance(Zee5TvBusinessLogic.java:11400)
	at com.business.zee.Zee5TvBusinessLogic.appLaunch(Zee5TvBusinessLogic.java:11669)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.appLaunchPerformance(TvZee5TvperformanceScripts.java:20)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:597)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>4</td>
<td>com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts@49c6c24f</td></tr>
<tr>
<td title='com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.deeplinkperformanace()'><b>deeplinkperformanace</b><br>Test class: com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts<br>Parameters: NonSubscribedUser</td>
<td><div><pre>java.lang.NullPointerException: Cannot invoke &quot;org.openqa.selenium.WebDriver.manage()&quot; because the return value of &quot;com.utility.Utilities.getWebDriver()&quot; is null
	at com.utility.Utilities.verifyIsElementDisplayed(Utilities.java:475)
	at com.business.zee.Zee5TvBusinessLogic.deepLink_Validation(Zee5TvBusinessLogic.java:12062)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.deeplinkperformanace(TvZee5TvperformanceScripts.java:44)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace287933084", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace287933084'><pre>java.lang.NullPointerException: Cannot invoke &quot;org.openqa.selenium.WebDriver.manage()&quot; because the return value of &quot;com.utility.Utilities.getWebDriver()&quot; is null
	at com.utility.Utilities.verifyIsElementDisplayed(Utilities.java:475)
	at com.business.zee.Zee5TvBusinessLogic.deepLink_Validation(Zee5TvBusinessLogic.java:12062)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.deeplinkperformanace(TvZee5TvperformanceScripts.java:44)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:597)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>22</td>
<td>com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts@49c6c24f</td></tr>
<tr>
<td title='com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.loginPerformance()'><b>loginPerformance</b><br>Test class: com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts</td>
<td><div><pre>java.lang.IllegalArgumentException: nodeName cannot be null or empty
	at com.aventstack.extentreports.ExtentTest.createNode(ExtentTest.java:166)
	at com.aventstack.extentreports.ExtentTest.createNode(ExtentTest.java:274)
	at com.extent.ExtentReporter.HeaderChildNode(ExtentReporter.java:247)
	at com.extent.ExtentReporter.onTestSkipped(ExtentReporter.java:237)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 24 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1336437944", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1336437944'><pre>java.lang.IllegalArgumentException: nodeName cannot be null or empty
	at com.aventstack.extentreports.ExtentTest.createNode(ExtentTest.java:166)
	at com.aventstack.extentreports.ExtentTest.createNode(ExtentTest.java:274)
	at com.extent.ExtentReporter.HeaderChildNode(ExtentReporter.java:247)
	at com.extent.ExtentReporter.onTestSkipped(ExtentReporter.java:237)
	at org.testng.internal.TestListenerHelper.runTestListeners(TestListenerHelper.java:57)
	at org.testng.internal.TestInvoker.runTestResultListener(TestInvoker.java:219)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:651)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>21</td>
<td>com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts@49c6c24f</td></tr>
<tr>
<td title='com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.navigationPerformance()'><b>navigationPerformance</b><br>Test class: com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts<br>Parameters: NonSubscribedUser</td>
<td><div><pre>java.lang.NullPointerException: Cannot invoke &quot;String.trim()&quot; because the return value of &quot;java.io.BufferedReader.readLine()&quot; is null
	at com.business.zee.Zee5TvBusinessLogic.Memory_UsagePerformance(Zee5TvBusinessLogic.java:11400)
	at com.business.zee.Zee5TvBusinessLogic.SelectTopNavigationTab_Timer(Zee5TvBusinessLogic.java:11913)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.navigationPerformance(TvZee5TvperformanceScripts.java:32)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace430845669", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace430845669'><pre>java.lang.NullPointerException: Cannot invoke &quot;String.trim()&quot; because the return value of &quot;java.io.BufferedReader.readLine()&quot; is null
	at com.business.zee.Zee5TvBusinessLogic.Memory_UsagePerformance(Zee5TvBusinessLogic.java:11400)
	at com.business.zee.Zee5TvBusinessLogic.SelectTopNavigationTab_Timer(Zee5TvBusinessLogic.java:11913)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.navigationPerformance(TvZee5TvperformanceScripts.java:32)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:597)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>60</td>
<td>com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts@49c6c24f</td></tr>
<tr>
<td title='com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.playbackperformanace()'><b>playbackperformanace</b><br>Test class: com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts<br>Parameters: NonSubscribedUser</td>
<td><div><pre>org.openqa.selenium.NoSuchElementException: no such element (An element could not be located on the page using the given search parameters (XPATH=&apos;//*[@id=&apos;search_icon_menu&apos;]&apos;))  (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: &apos;3.141.59&apos;, revision: &apos;e82be7d358&apos;, time: &apos;2018-11-14T08:17:03&apos;
System info: host: &apos;DESKTOP-UFUPRO6&apos;, ip: &apos;*********&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.1&apos;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.zee5.player.activities...., appBuildVersion: , appPackage: com.graymatrix.did, appReleaseVersion: , appiumVersion: 1.8.0, applicationClearData: false, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, commandTimeouts: 120000, desired: {appActivity: com.zee5.player.activities...., appPackage: com.graymatrix.did, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: STB, device.majorVersion: 9, device.manufacture: skyworth, device.model: Y Series, device.name: Y Series, device.os: Android, device.screenSize: 1280x720, device.serialNumber: *********:5555, device.version: 9, deviceName: Android, deviceUDID: *********:5555, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, installOnlyForUpdate: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudio-r..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 12/06/21 01:35 PM, udid: *********:5555, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: b274e883-8356-4b96-92d1-be2b3f6ba051
*** Element info: {Using=xpath, value=//*[@id=&apos;search_icon_menu&apos;]}
	at org.openqa.selenium.remote.ErrorHandler.createThrowable(ErrorHandler.java:214)
	at org.openqa.selenium.remote.ErrorHandler.throwIfResponseFailed(ErrorHandler.java:166)
	at org.openqa.selenium.remote.http.JsonHttpResponseCodec.reconstructValue(JsonHttpResponseCodec.java:40)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:80)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:44)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at com.utility.Utilities.TVgetAttributValue(Utilities.java:2197)
	at com.business.zee.Zee5TvBusinessLogic.Performance_InitiateContentPlayback(Zee5TvBusinessLogic.java:12181)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.playbackperformanace(TvZee5TvperformanceScripts.java:38)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 32 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2000856156", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2000856156'><pre>org.openqa.selenium.NoSuchElementException: no such element (An element could not be located on the page using the given search parameters (XPATH=&apos;//*[@id=&apos;search_icon_menu&apos;]&apos;))  (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: &apos;3.141.59&apos;, revision: &apos;e82be7d358&apos;, time: &apos;2018-11-14T08:17:03&apos;
System info: host: &apos;DESKTOP-UFUPRO6&apos;, ip: &apos;*********&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.1&apos;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.zee5.player.activities...., appBuildVersion: , appPackage: com.graymatrix.did, appReleaseVersion: , appiumVersion: 1.8.0, applicationClearData: false, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, commandTimeouts: 120000, desired: {appActivity: com.zee5.player.activities...., appPackage: com.graymatrix.did, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: STB, device.majorVersion: 9, device.manufacture: skyworth, device.model: Y Series, device.name: Y Series, device.os: Android, device.screenSize: 1280x720, device.serialNumber: *********:5555, device.version: 9, deviceName: Android, deviceUDID: *********:5555, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, installOnlyForUpdate: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudio-r..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 12/06/21 01:35 PM, udid: *********:5555, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: b274e883-8356-4b96-92d1-be2b3f6ba051
*** Element info: {Using=xpath, value=//*[@id=&apos;search_icon_menu&apos;]}
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at org.openqa.selenium.remote.ErrorHandler.createThrowable(ErrorHandler.java:214)
	at org.openqa.selenium.remote.ErrorHandler.throwIfResponseFailed(ErrorHandler.java:166)
	at org.openqa.selenium.remote.http.JsonHttpResponseCodec.reconstructValue(JsonHttpResponseCodec.java:40)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:80)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:44)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at com.utility.Utilities.TVgetAttributValue(Utilities.java:2197)
	at com.business.zee.Zee5TvBusinessLogic.Performance_InitiateContentPlayback(Zee5TvBusinessLogic.java:12181)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.playbackperformanace(TvZee5TvperformanceScripts.java:38)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:597)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>33</td>
<td>com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts@49c6c24f</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.loginPerformance()'><b>loginPerformance</b><br>Test class: com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts</td>
<td><div><pre>org.testng.SkipException: Device not connected OR Appium Studio service is down...
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:76)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:49)
	at com.business.zee.Zee5TvBusinessLogic.&lt;init&gt;(Zee5TvBusinessLogic.java:49)
	at com.business.zee.Zee5TvBusinessLogic.login(Zee5TvBusinessLogic.java:599)
	at com.business.zee.Zee5TvBusinessLogic.loginPerformance(Zee5TvBusinessLogic.java:11768)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.loginPerformance(TvZee5TvperformanceScripts.java:26)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1777163938", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1777163938'><pre>org.testng.SkipException: Device not connected OR Appium Studio service is down...
	at com.driverInstance.DriverInstance.&lt;init&gt;(DriverInstance.java:76)
	at com.driverInstance.CommandBase.&lt;init&gt;(CommandBase.java:49)
	at com.business.zee.Zee5TvBusinessLogic.&lt;init&gt;(Zee5TvBusinessLogic.java:49)
	at com.business.zee.Zee5TvBusinessLogic.login(Zee5TvBusinessLogic.java:599)
	at com.business.zee.Zee5TvBusinessLogic.loginPerformance(Zee5TvBusinessLogic.java:11768)
	at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.loginPerformance(TvZee5TvperformanceScripts.java:26)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:597)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>21</td>
<td>com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts@49c6c24f</td></tr>
</table><p>
</body>
</html>