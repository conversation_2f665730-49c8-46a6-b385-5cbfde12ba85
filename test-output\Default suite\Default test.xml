<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="DESKTOP-077G3EM" failures="7" tests="7" name="Default test" time="0.04" errors="0" timestamp="2022-09-20T13:18:45 IST">
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="EmptyClientId_TokenGCash" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method EmptyClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method EmptyClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- EmptyClientId_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="EmptyClientSecret_TokenGCash" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method EmptyClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method EmptyClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- EmptyClientSecret_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="EmptyGrantType_TokenGCash" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method EmptyGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method EmptyGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- EmptyGrantType_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="InvalidClientId_TokenGCash" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method InvalidClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method InvalidClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- InvalidClientId_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="InvalidClientSecret_TokenGCash" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method InvalidClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method InvalidClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- InvalidClientSecret_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="InvalidGrantType_TokenGCash" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method InvalidGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method InvalidGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- InvalidGrantType_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="TokenGCash_200" time="0.0">
    <failure type="org.testng.TestNGException" message="
Parameter &amp;apos;APIUrl&amp;apos; is required by @Test on method TokenGCash_200 but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml">
      <![CDATA[org.testng.TestNGException: 
Parameter 'APIUrl' is required by @Test on method TokenGCash_200 but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- TokenGCash_200 -->
</testsuite> <!-- Default test -->
