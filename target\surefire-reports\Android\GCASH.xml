<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="<PERSON><PERSON><PERSON><PERSON>" failures="0" tests="7" name="GCASH" time="0.651" errors="0" timestamp="2025-07-31T15:23:14 CST">
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="@BeforeTest Before" time="0.306">
    <failure type="java.lang.IndexOutOfBoundsException" message="Index 0 out of bounds for length 0">
      <![CDATA[java.lang.IndexOutOfBoundsException: Index 0 out of bounds for length 0
at java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
at java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
at java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
at java.base/java.util.Objects.checkIndex(Objects.java:365)
at java.base/java.util.ArrayList.get(ArrayList.java:428)
at com.driverInstance.Drivertools.<init>(Drivertools.java:293)
at com.driverInstance.DriverInstance.<init>(DriverInstance.java:36)
at com.driverInstance.CommandBase.<init>(CommandBase.java:51)
at com.business.gCASH.GCASHBusinessLogic.<init>(GCASHBusinessLogic.java:37)
at com.GCash_GGivesScripts.GCASHScripts.Before(GCASHScripts.java:21)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeTest Before -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashToken" time="0.0">
    <skipped/>
  </testcase> <!-- GCashToken -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashClientId" time="0.0">
    <skipped/>
  </testcase> <!-- GCashClientId -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashEmptyId" time="0.0">
    <skipped/>
  </testcase> <!-- GCashEmptyId -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashSecretId" time="0.0">
    <skipped/>
  </testcase> <!-- GCashSecretId -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashInvalidSecret" time="0.0">
    <skipped/>
  </testcase> <!-- GCashInvalidSecret -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashGrant" time="0.0">
    <skipped/>
  </testcase> <!-- GCashGrant -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="GCashEmptyGrant" time="0.0">
    <skipped/>
  </testcase> <!-- GCashEmptyGrant -->
</testsuite> <!-- GCASH -->
