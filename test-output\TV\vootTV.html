<html>
<head>
<title>TestNG:  vootTV</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>vootTV</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/1/0</td>
</tr><tr>
<td>Started on:</td><td>Thu Jul 14 09:45:28 IST 2022</td>
</tr>
<tr><td>Total time:</td><td>896 seconds (896076 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.VootTvScripts.TvVootScripts.premiumPage()'><b>premiumPage</b><br>Test class: com.VootTvScripts.TvVootScripts<br>Parameters: Guest</td>
<td><div><pre>org.openqa.selenium.NoSuchElementException: An element could not be located on the page using the given search parameters.
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: &apos;3.141.59&apos;, revision: &apos;e82be7d358&apos;, time: &apos;2018-11-14T08:17:03&apos;
System info: host: &apos;LAPTOP-7AIFL2R7&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.1&apos;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, databaseEnabled: false, desired: {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: checkTv, fullReset: false, newCommandTimeout: 300, platformName: android}, deviceApiLevel: 30, deviceManufacturer: Google, deviceModel: AOSP TV on x86, deviceName: emulator-5554, deviceScreenDensity: 213, deviceScreenSize: 1280x720, deviceUDID: emulator-5554, fullReset: false, javascriptEnabled: true, locationContextEnabled: false, networkConnectionEnabled: true, newCommandTimeout: 300, pixelRatio: 1.3312501, platform: LINUX, platformName: Android, platformVersion: 11, statBarHeight: 0, takesScreenshot: true, viewportRect: {height: 720, left: 0, top: 0, width: 1280}, warnings: {}, webStorageEnabled: false}
Session ID: 75ffb4ee-4e1f-4340-858b-55dce5b9a479
*** Element info: {Using=xpath, value=//*[@text=&apos;Halo&apos;]}
	at org.openqa.selenium.remote.http.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:187)
	at org.openqa.selenium.remote.http.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:122)
	at org.openqa.selenium.remote.http.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:49)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at com.utility.Utilities.TVgetText(Utilities.java:2157)
	at com.business.voot.VootTvBusinessLogic.premiunPageValidation(VootTvBusinessLogic.java:525)
	at com.VootTvScripts.TvVootScripts.premiumPage(TvVootScripts.java:30)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 32 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1772797104", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1772797104'><pre>org.openqa.selenium.NoSuchElementException: An element could not be located on the page using the given search parameters.
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: &apos;3.141.59&apos;, revision: &apos;e82be7d358&apos;, time: &apos;2018-11-14T08:17:03&apos;
System info: host: &apos;LAPTOP-7AIFL2R7&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;17.0.1&apos;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, databaseEnabled: false, desired: {appActivity: com.viacom18.voottv.splash...., appPackage: com.viacom18.tv.voot, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: checkTv, fullReset: false, newCommandTimeout: 300, platformName: android}, deviceApiLevel: 30, deviceManufacturer: Google, deviceModel: AOSP TV on x86, deviceName: emulator-5554, deviceScreenDensity: 213, deviceScreenSize: 1280x720, deviceUDID: emulator-5554, fullReset: false, javascriptEnabled: true, locationContextEnabled: false, networkConnectionEnabled: true, newCommandTimeout: 300, pixelRatio: 1.3312501, platform: LINUX, platformName: Android, platformVersion: 11, statBarHeight: 0, takesScreenshot: true, viewportRect: {height: 720, left: 0, top: 0, width: 1280}, warnings: {}, webStorageEnabled: false}
Session ID: 75ffb4ee-4e1f-4340-858b-55dce5b9a479
*** Element info: {Using=xpath, value=//*[@text=&apos;Halo&apos;]}
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at org.openqa.selenium.remote.http.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:187)
	at org.openqa.selenium.remote.http.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:122)
	at org.openqa.selenium.remote.http.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:49)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at com.utility.Utilities.TVgetText(Utilities.java:2157)
	at com.business.voot.VootTvBusinessLogic.premiunPageValidation(VootTvBusinessLogic.java:525)
	at com.VootTvScripts.TvVootScripts.premiumPage(TvVootScripts.java:30)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:134)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:597)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:816)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>212</td>
<td>com.VootTvScripts.TvVootScripts@878452d</td></tr>
</table><p>
</body>
</html>