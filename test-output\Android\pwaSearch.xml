<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="pwaSearch" tests="1" failures="1" timestamp="9 Apr 2020 07:41:25 GMT" time="180.03" errors="0">
  <testcase name="Search" time="109.62" classname="com.zee5.PWAScripts.Zee5PWASearch">
    <failure type="org.openqa.selenium.TimeoutException" message="Expected condition failed: waiting for presence of element located by: By.xpath: (//div[@class=&amp;apos;metaInfo&amp;apos;]/child::p)[1] (tried for 0 second(s) with 500 milliseconds interval)">
      <![CDATA[org.openqa.selenium.TimeoutException: Expected condition failed: waiting for presence of element located by: By.xpath: (//div[@class='metaInfo']/child::p)[1] (tried for 0 second(s) with 500 milliseconds interval)
	at org.openqa.selenium.support.ui.WebDriverWait.timeoutException(WebDriverWait.java:95)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:272)
	at com.utility.Utilities.findElement(Utilities.java:120)
	at com.utility.Utilities.getText(Utilities.java:299)
	at com.business.zee.Zee5PWABusinessLogic.navigationToConsumptionScreenThroughTrendingSearches(Zee5PWABusinessLogic.java:1086)
	at com.zee5.PWAScripts.Zee5PWASearch.Search(Zee5PWASearch.java:25)
Caused by: org.openqa.selenium.NoSuchElementException: no such element: Unable to locate element: {"method":"xpath","selector":"(//div[@class='metaInfo']/child::p)[1]"}
  (Session info: chrome=80.0.3987.149)
  (Driver info: chromedriver=2.41.578737 (49da6702b16031c40d63e5618de03a32ff6c197e),platform=Windows NT 10.0.18362 x86_64) (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:17:03'
System info: host: 'LAPTOP-NRJMSC7T', ip: '*************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_231'
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.google.android.apps.chr..., appBuildVersion: , appPackage: com.android.chrome, appReleaseVersion: , appiumVersion: 1.8.0, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, browserName: Chrome, commandTimeouts: 120000, compressXml: true, desired: {appActivity: com.google.android.apps.chr..., appPackage: com.android.chrome, autoAcceptAlerts: true, automationName: uiautomator2, browserName: Chrome, compressXml: true, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: UNKNOWN, device.majorVersion: 8, device.manufacture: vivo, device.model: vivo 1820, device.name: vivo 1820, device.os: Android, device.screenSize: 720x1520, device.serialNumber: OVS87SCAIRW8B6LN, device.version: 8.1.0, deviceName: Android, deviceUDID: OVS87SCAIRW8B6LN, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudioen..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 04/09/20 01:08 PM, udid: OVS87SCAIRW8B6LN, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: 9523e9f7-44bb-4c74-9678-bcb6d45ba622
*** Element info: {Using=xpath, value=(//div[@class='metaInfo']/child::p)[1]}
	at org.openqa.selenium.remote.ErrorHandler.createThrowable(ErrorHandler.java:214)
	at org.openqa.selenium.remote.ErrorHandler.throwIfResponseFailed(ErrorHandler.java:166)
	at org.openqa.selenium.remote.http.JsonHttpResponseCodec.reconstructValue(JsonHttpResponseCodec.java:40)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:80)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:44)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.support.ui.ExpectedConditions$6.apply(ExpectedConditions.java:182)
	at org.openqa.selenium.support.ui.ExpectedConditions$6.apply(ExpectedConditions.java:179)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:249)
	... 28 more
... Removed 28 stack frames]]>
    </failure>
  </testcase> <!-- Search -->
</testsuite> <!-- pwaSearch -->
