<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [Suite]">
  <parameter name="runModule" value="Suite"/>
  <parameter name="userType" value="Guest"/>
  <parameter name="url" value="https://newpwa.zee5.com/"/>
  <parameter name="runMode" value="Suites"/>
  <parameter name="browserType" value="Chrome"/>
  <test thread-count="5" name="VerifyURL(failed)">
    <classes>
      <class name="com.VerifyURL.VerifyURL">
        <methods>
          <include name="init"/>
          <include name="verifyPageresponse"/>
        </methods>
      </class> <!-- com.VerifyURL.VerifyURL -->
    </classes>
  </test> <!-- VerifyURL(failed) -->
</suite> <!-- Failed suite [Suite] -->
