<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>TV</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="ebfd63">  <td>22/09/01 15:27:48</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="MLWalletScripts.Allowpopup(java.lang.String)[pri:0, instance:com.MLWalletScripts.MLWalletScripts@426b6a74]">Allowpopup</td> 
  <td>main@683718244</td>   <td></td> </tr>
<tr bgcolor="ebfd63">  <td>22/09/01 15:27:37</td>   <td>-11098</td> <td>&nbsp;</td><td title="&gt;&gt;MLWalletScripts.Before()[pri:0, instance:com.MLWalletScripts.MLWalletScripts@426b6a74]">&gt;&gt;Before</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@683718244</td>   <td></td> </tr>
<tr bgcolor="ebfd63">  <td>22/09/01 15:27:50</td>   <td>1961</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="MLWalletScripts.Login(java.lang.String)[pri:1, instance:com.MLWalletScripts.MLWalletScripts@426b6a74]">Login</td> 
  <td>main@683718244</td>   <td></td> </tr>
<tr bgcolor="ebfd63">  <td>22/09/01 15:28:49</td>   <td>60527</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="MLWalletScripts.sendMoney()[pri:2, instance:com.MLWalletScripts.MLWalletScripts@426b6a74]">sendMoney</td> 
  <td>main@683718244</td>   <td></td> </tr>
<tr bgcolor="ebfd63">  <td>22/09/01 15:29:06</td>   <td>78024</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="MLWalletScripts.walletTowallet()[pri:3, instance:com.MLWalletScripts.MLWalletScripts@426b6a74]">walletTowallet</td> 
  <td>main@683718244</td>   <td></td> </tr>
<tr bgcolor="ebfd63">  <td>22/09/01 15:31:02</td>   <td>193503</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="MLWalletScripts.withdrawMoney()[pri:3, instance:com.MLWalletScripts.MLWalletScripts@426b6a74]">withdrawMoney</td> 
  <td>main@683718244</td>   <td></td> </tr>
</table>
