<?xml version="1.0" encoding="UTF-8"?>
<testng-results skipped="0" failed="0" ignored="0" total="7" passed="7">
  <reporter-output>
  </reporter-output>
  <suite name="Android" duration-ms="124773" started-at="2022-09-22T11:07:58 IST" finished-at="2022-09-22T11:10:03 IST">
    <groups>
    </groups>
    <test name="GCASH" duration-ms="124773" started-at="2022-09-22T11:07:58 IST" finished-at="2022-09-22T11:10:03 IST">
      <class name="com.GCash_GGivesScripts.GCASHScripts">
        <test-method status="PASS" signature="Before()[pri:0, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="Before" is-config="true" duration-ms="19399" started-at="2022-09-22T11:08:08 IST" finished-at="2022-09-22T11:08:27 IST">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- Before -->
        <test-method status="PASS" signature="GCashToken(java.lang.String)[pri:0, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashToken" duration-ms="3188" started-at="2022-09-22T11:08:27 IST" finished-at="2022-09-22T11:08:31 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashToken -->
        <test-method status="PASS" signature="GCashClientId(java.lang.String)[pri:1, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashClientId" duration-ms="682" started-at="2022-09-22T11:08:42 IST" finished-at="2022-09-22T11:08:42 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashClientId -->
        <test-method status="PASS" signature="GCashEmptyId(java.lang.String)[pri:2, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashEmptyId" duration-ms="783" started-at="2022-09-22T11:09:00 IST" finished-at="2022-09-22T11:09:00 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashEmptyId -->
        <test-method status="PASS" signature="GCashSecretId(java.lang.String)[pri:3, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashSecretId" duration-ms="629" started-at="2022-09-22T11:09:16 IST" finished-at="2022-09-22T11:09:17 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashSecretId -->
        <test-method status="PASS" signature="GCashInvalidSecret(java.lang.String)[pri:4, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashInvalidSecret" duration-ms="566" started-at="2022-09-22T11:09:30 IST" finished-at="2022-09-22T11:09:30 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashInvalidSecret -->
        <test-method status="PASS" signature="GCashGrant(java.lang.String)[pri:5, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashGrant" duration-ms="742" started-at="2022-09-22T11:09:41 IST" finished-at="2022-09-22T11:09:41 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashGrant -->
        <test-method status="PASS" signature="GCashEmptyGrant(java.lang.String)[pri:6, instance:com.GCash_GGivesScripts.GCASHScripts@910148]" name="GCashEmptyGrant" duration-ms="492" started-at="2022-09-22T11:09:51 IST" finished-at="2022-09-22T11:09:52 IST">
          <params>
            <param index="0">
              <value>
                <![CDATA[https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- GCashEmptyGrant -->
      </class> <!-- com.GCash_GGivesScripts.GCASHScripts -->
    </test> <!-- GCASH -->
  </suite> <!-- Android -->
</testng-results>
