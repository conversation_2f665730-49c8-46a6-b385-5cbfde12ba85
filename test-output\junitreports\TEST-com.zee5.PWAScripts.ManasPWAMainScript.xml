<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWAScripts.ManasPWAMainScript" tests="8" failures="1" timestamp="14 Apr 2020 12:31:32 GMT" time="2687.221" errors="2">
  <testcase name="PWAOnboarding" time="139.089" classname="com.zee5.PWAScripts.ManasPWAMainScript"/>
  <testcase name="PWALandingScreen" time="45.107" classname="com.zee5.PWAScripts.ManasPWAMainScript"/>
  <testcase name="PWACarousel" time="89.194" classname="com.zee5.PWAScripts.ManasPWAMainScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Navigated to respective Live Channel Consumption screen expected [false] but found [true]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Navigated to respective Live Channel Consumption screen expected [false] but found [true]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.business.zee.Zee5PWABusinessLogic.verifyAutoroatingOnCarousel(Zee5PWABusinessLogic.java:2107)
	at com.zee5.PWAScripts.ManasPWAMainScript.PWACarousel(ManasPWAMainScript.java:59)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWACarousel -->
  <testcase name="PWAConsumptionsScreen" time="1591.184" classname="com.zee5.PWAScripts.ManasPWAMainScript"/>
  <testcase name="PWAPlayer" time="279.566" classname="com.zee5.PWAScripts.ManasPWAMainScript">
    <error type="org.openqa.selenium.TimeoutException" message="Expected condition failed: waiting for element to no longer be visible: By.xpath: //*[@class=&#039;playkit-icon playkit-icon-play&#039;] (tried for 0 second(s) with 500 milliseconds interval)
Build info: version: &#039;3.141.59&#039;, revision: &#039;e82be7d358&#039;, time: &#039;2018-11-14T08:17:03&#039;
System info: host: &#039;LAPTOP-NRJMSC7T&#039;, ip: &#039;*************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_231&#039;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.google.android.apps.chr..., appBuildVersion: , appPackage: com.android.chrome, appReleaseVersion: , appiumVersion: 1.8.0, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, browserName: Chrome, commandTimeouts: 120000, compressXml: true, desired: {appActivity: com.google.android.apps.chr..., appPackage: com.android.chrome, autoAcceptAlerts: true, automationName: uiautomator2, browserName: Chrome, compressXml: true, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: UNKNOWN, device.majorVersion: 8, device.manufacture: vivo, device.model: vivo 1820, device.name: vivo 1820, device.os: Android, device.screenSize: 720x1520, device.serialNumber: OVS87SCAIRW8B6LN, device.version: 8.1.0, deviceName: Android, deviceUDID: OVS87SCAIRW8B6LN, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudioen..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 04/14/20 05:15 PM, udid: OVS87SCAIRW8B6LN, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: 17a311ba-a5cc-4294-8f39-f871342c980e">
      <![CDATA[org.openqa.selenium.TimeoutException: Expected condition failed: waiting for element to no longer be visible: By.xpath: //*[@class='playkit-icon playkit-icon-play'] (tried for 0 second(s) with 500 milliseconds interval)
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:17:03'
System info: host: 'LAPTOP-NRJMSC7T', ip: '*************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_231'
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.google.android.apps.chr..., appBuildVersion: , appPackage: com.android.chrome, appReleaseVersion: , appiumVersion: 1.8.0, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, browserName: Chrome, commandTimeouts: 120000, compressXml: true, desired: {appActivity: com.google.android.apps.chr..., appPackage: com.android.chrome, autoAcceptAlerts: true, automationName: uiautomator2, browserName: Chrome, compressXml: true, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: UNKNOWN, device.majorVersion: 8, device.manufacture: vivo, device.model: vivo 1820, device.name: vivo 1820, device.os: Android, device.screenSize: 720x1520, device.serialNumber: OVS87SCAIRW8B6LN, device.version: 8.1.0, deviceName: Android, deviceUDID: OVS87SCAIRW8B6LN, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudioen..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 04/14/20 05:15 PM, udid: OVS87SCAIRW8B6LN, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: 17a311ba-a5cc-4294-8f39-f871342c980e
	at org.openqa.selenium.support.ui.WebDriverWait.timeoutException(WebDriverWait.java:95)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:272)
	at com.utility.Utilities.verifyElementNotPresent(Utilities.java:183)
	at com.business.zee.Zee5PWABusinessLogic.playerValidations(Zee5PWABusinessLogic.java:2874)
	at com.zee5.PWAScripts.ManasPWAMainScript.PWAPlayer(ManasPWAMainScript.java:88)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- PWAPlayer -->
  <testcase name="PWASubscription" time="179.023" classname="com.zee5.PWAScripts.ManasPWAMainScript">
    <error type="org.openqa.selenium.TimeoutException" message="Expected condition failed: waiting for presence of element located by: By.xpath: //div[contains(@class,&#039;searchCategoryLanding&#039;)]//h3[contains(@class,&#039;cardTitle&#039;)]//span[contains(@class,&#039;highLight&#039;)] (tried for 0 second(s) with 500 milliseconds interval)">
      <![CDATA[org.openqa.selenium.TimeoutException: Expected condition failed: waiting for presence of element located by: By.xpath: //div[contains(@class,'searchCategoryLanding')]//h3[contains(@class,'cardTitle')]//span[contains(@class,'highLight')] (tried for 0 second(s) with 500 milliseconds interval)
	at org.openqa.selenium.support.ui.WebDriverWait.timeoutException(WebDriverWait.java:95)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:272)
	at com.utility.Utilities.findElement(Utilities.java:120)
	at com.business.zee.Zee5PWABusinessLogic.zeeSearchForContentAndClickOnFirstResult(Zee5PWABusinessLogic.java:3944)
	at com.business.zee.Zee5PWABusinessLogic.zeePWASubscriptionScenariosValidation(Zee5PWABusinessLogic.java:4467)
	at com.business.zee.Zee5PWABusinessLogic.zeePWASubscriptionSuite(Zee5PWABusinessLogic.java:3929)
	at com.zee5.PWAScripts.ManasPWAMainScript.PWASubscription(ManasPWAMainScript.java:104)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
Caused by: org.openqa.selenium.NoSuchElementException: no such element: Unable to locate element: {"method":"xpath","selector":"//div[contains(@class,'searchCategoryLanding')]//h3[contains(@class,'cardTitle')]//span[contains(@class,'highLight')]"}
  (Session info: chrome=80.0.3987.149)
  (Driver info: chromedriver=2.41.578737 (49da6702b16031c40d63e5618de03a32ff6c197e),platform=Windows NT 10.0.18362 x86_64) (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:17:03'
System info: host: 'LAPTOP-NRJMSC7T', ip: '*************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_231'
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.google.android.apps.chr..., appBuildVersion: , appPackage: com.android.chrome, appReleaseVersion: , appiumVersion: 1.8.0, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, browserName: Chrome, commandTimeouts: 120000, compressXml: true, desired: {appActivity: com.google.android.apps.chr..., appPackage: com.android.chrome, autoAcceptAlerts: true, automationName: uiautomator2, browserName: Chrome, compressXml: true, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: UNKNOWN, device.majorVersion: 8, device.manufacture: vivo, device.model: vivo 1820, device.name: vivo 1820, device.os: Android, device.screenSize: 720x1520, device.serialNumber: OVS87SCAIRW8B6LN, device.version: 8.1.0, deviceName: Android, deviceUDID: OVS87SCAIRW8B6LN, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudioen..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 04/14/20 05:15 PM, udid: OVS87SCAIRW8B6LN, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: 17a311ba-a5cc-4294-8f39-f871342c980e
*** Element info: {Using=xpath, value=//div[contains(@class,'searchCategoryLanding')]//h3[contains(@class,'cardTitle')]//span[contains(@class,'highLight')]}
	at sun.reflect.GeneratedConstructorAccessor15.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.openqa.selenium.remote.ErrorHandler.createThrowable(ErrorHandler.java:214)
	at org.openqa.selenium.remote.ErrorHandler.throwIfResponseFailed(ErrorHandler.java:166)
	at org.openqa.selenium.remote.http.JsonHttpResponseCodec.reconstructValue(JsonHttpResponseCodec.java:40)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:80)
	at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:44)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
	at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
	at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
	at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
	at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
	at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
	at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
	at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
	at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
	at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
	at org.openqa.selenium.support.ui.ExpectedConditions$6.apply(ExpectedConditions.java:182)
	at org.openqa.selenium.support.ui.ExpectedConditions$6.apply(ExpectedConditions.java:179)
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:249)
	... 29 more
]]>
    </error>
  </testcase> <!-- PWASubscription -->
  <testcase name="PWAUICheck" time="182.406" classname="com.zee5.PWAScripts.ManasPWAMainScript"/>
  <testcase name="PWASearch" time="181.652" classname="com.zee5.PWAScripts.ManasPWAMainScript"/>
</testsuite> <!-- com.zee5.PWAScripts.ManasPWAMainScript -->
