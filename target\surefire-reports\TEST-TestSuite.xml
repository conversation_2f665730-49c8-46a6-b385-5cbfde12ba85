<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="TestSuite" time="126.084" tests="7" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="JOB_NAME" value="GCash_Api_Automation"/>
    <property name="RUN_TESTS_DISPLAY_URL" value="http://localhost:8080/job/GCash_Api_Automation/8/display/redirect?page=tests"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="NUMBER_OF_PROCESSORS" value="8"/>
    <property name="sun.management.compiler" value="HotSpot Client Compiler"/>
    <property name="JENKINS_HOME" value="C:\Users\<USER>\.jenkins"/>
    <property name="HUDSON_SERVER_COOKIE" value="a778e2eecc1bea5f"/>
    <property name="POM_ARTIFACTID" value="GCASH_TAF"/>
    <property name="os.name" value="Windows 10"/>
    <property name="sun.boot.class.path" value="C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\resources.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\rt.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\sunrsasign.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\jsse.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\jce.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\charsets.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\jfr.jar;C:\Program Files (x86)\Java\jdk1.8.0_202\jre\classes"/>
    <property name="EXECUTOR_NUMBER" value="0"/>
    <property name="sun.desktop" value="windows"/>
    <property name="PATHEXT" value=".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="POM_DISPLAYNAME" value="SeetestAutomation"/>
    <property name="NODE_LABELS" value="built-in"/>
    <property name="TEMP" value="C:\Windows\TEMP"/>
    <property name="java.runtime.version" value="1.8.0_202-b08"/>
    <property name="WINSW_SERVICE_ID" value="jenkins"/>
    <property name="user.name" value="DESKTOP-077G3EM$"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="CommonProgramW6432" value="C:\Program Files\Common Files"/>
    <property name="ProgramFiles" value="C:\Program Files (x86)"/>
    <property name="user.language" value="en"/>
    <property name="JOB_BASE_NAME" value="GCash_Api_Automation"/>
    <property name="RUN_CHANGES_DISPLAY_URL" value="http://localhost:8080/job/GCash_Api_Automation/8/display/redirect?page=changes"/>
    <property name="BUILD_DISPLAY_NAME" value="#8"/>
    <property name="sun.boot.library.path" value="C:\Program Files (x86)\Java\jdk1.8.0_202\jre\bin"/>
    <property name="GIT_PREVIOUS_SUCCESSFUL_COMMIT" value="50c8d84deaf8e2dc4045763b0913aaf6c472d572"/>
    <property name="LOCALAPPDATA" value="C:\Windows\system32\config\systemprofile\AppData\Local"/>
    <property name="java.version" value="1.8.0_202"/>
    <property name="PROCESSOR_LEVEL" value="6"/>
    <property name="user.timezone" value="Asia/Calcutta"/>
    <property name="USERNAME" value="DESKTOP-077G3EM$"/>
    <property name="sun.arch.data.model" value="32"/>
    <property name="NODE_NAME" value="built-in"/>
    <property name="PROCESSOR_ARCHITEW6432" value="AMD64"/>
    <property name="java.endorsed.dirs" value="C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\endorsed"/>
    <property name="BUILD_ID" value="8"/>
    <property name="sun.cpu.isalist" value="pentium_pro+mmx pentium_pro pentium+mmx pentium i486 i386 i86"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="file.separator" value="\"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="maven.conf" value="C:\Program Files\apache-maven-3.8.6\conf"/>
    <property name="java.class.version" value="52.0"/>
    <property name="org.slf4j.simpleLogger.defaultLogLevel" value="info"/>
    <property name="user.country" value="US"/>
    <property name="ALLUSERSPROFILE" value="C:\ProgramData"/>
    <property name="java.home" value="C:\Program Files (x86)\Java\jdk1.8.0_202\jre"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="os.version" value="10.0"/>
    <property name="RUN_DISPLAY_URL" value="http://localhost:8080/job/GCash_Api_Automation/8/display/redirect"/>
    <property name="POM_VERSION" value="1.0-SNAPSHOT"/>
    <property name="TMP" value="C:\Windows\TEMP"/>
    <property name="RUN_ARTIFACTS_DISPLAY_URL" value="http://localhost:8080/job/GCash_Api_Automation/8/display/redirect?page=artifacts"/>
    <property name="path.separator" value=";"/>
    <property name="java.vm.version" value="25.202-b08"/>
    <property name="user.variant" value=""/>
    <property name="GIT_PREVIOUS_COMMIT" value="50c8d84deaf8e2dc4045763b0913aaf6c472d572"/>
    <property name="JOB_DISPLAY_URL" value="http://localhost:8080/job/GCash_Api_Automation/display/redirect"/>
    <property name="CommonProgramFiles(x86)" value="C:\Program Files (x86)\Common Files"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="JAVA_HOME" value="C:\Program Files (x86)\Java\jdk1.8.0_202"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="CommonProgramFiles" value="C:\Program Files (x86)\Common Files"/>
    <property name="ComSpec" value="C:\Windows\system32\cmd.exe"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="POM_PACKAGING" value="jar"/>
    <property name="user.script" value=""/>
    <property name="COMPUTERNAME" value="DESKTOP-077G3EM"/>
    <property name="SERVICE_ID" value="jenkins"/>
    <property name="POM_GROUPID" value="GCASH_TAF"/>
    <property name="user.home" value="C:\Windows\system32\config\systemprofile"/>
    <property name="PUBLIC" value="C:\Users\<USER>\ProgramData"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="USERPROFILE" value="C:\Windows\system32\config\systemprofile"/>
    <property name="CLASSPATH" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="java.library.path" value="C:\Program Files (x86)\Java\jdk1.8.0_202\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\apache-maven-3.8.6/bin;C:\Program Files (x86)\Java\jdk1.8.0_202/bin;C:\Program Files (x86)\Java\jdk1.8.0_202/bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\Program Files\apache-maven-3.8.6\bin;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;."/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="APPDATA" value="C:\Windows\system32\config\systemprofile\AppData\Roaming"/>
    <property name="ZES_ENABLE_SYSMAN" value="1"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="maven.home" value="C:\Program Files\apache-maven-3.8.6"/>
    <property name="java.class.path" value="C:\Users\<USER>\.jenkins\plugins\maven-plugin\WEB-INF\lib\maven35-agent-1.13.jar;C:\Program Files\apache-maven-3.8.6\boot\plexus-classworlds-2.6.0.jar;C:\Program Files\apache-maven-3.8.6/conf/logging"/>
    <property name="sun.java.command" value="jenkins.maven3.agent.Maven35Main C:\Program Files\apache-maven-3.8.6 C:\Users\<USER>\.jenkins\war\WEB-INF\lib\remoting-4.13.3.jar C:\Users\<USER>\.jenkins\plugins\maven-plugin\WEB-INF\lib\maven35-interceptor-1.13.jar C:\Users\<USER>\.jenkins\plugins\maven-plugin\WEB-INF\lib\maven3-interceptor-commons-1.13.jar 64843"/>
    <property name="Path" value="C:\Program Files (x86)\Java\jdk1.8.0_202/bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\Program Files\apache-maven-3.8.6\bin;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PROCESSOR_ARCHITECTURE" value="x86"/>
    <property name="jansi.mode" value="strip"/>
    <property name="PATH+JDK" value="C:\Program Files (x86)\Java\jdk1.8.0_202/bin"/>
    <property name="PROCESSOR_IDENTIFIER" value="Intel64 Family 6 Model 140 Stepping 1, GenuineIntel"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="M2_HOME" value="C:\Program Files\apache-maven-3.8.6"/>
    <property name="java.io.tmpdir" value="C:\Windows\TEMP\"/>
    <property name="CI" value="true"/>
    <property name="OS" value="Windows_NT"/>
    <property name="HUDSON_HOME" value="C:\Users\<USER>\.jenkins"/>
    <property name="PATH+MAVEN" value="C:\Program Files\apache-maven-3.8.6/bin"/>
    <property name="ProgramFiles(x86)" value="C:\Program Files (x86)"/>
    <property name="BUILD_URL" value="http://localhost:8080/job/GCash_Api_Automation/8/"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="os.arch" value="x86"/>
    <property name="SystemRoot" value="C:\Windows"/>
    <property name="java.ext.dirs" value="C:\Program Files (x86)\Java\jdk1.8.0_202\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\API_GCash\GCash_API_Project"/>
    <property name="JOB_URL" value="http://localhost:8080/job/GCash_Api_Automation/"/>
    <property name="USERDOMAIN" value="WORKGROUP"/>
    <property name="line.separator" value="&#10;"/>
    <property name="MAVEN_HOME" value="C:\Program Files\apache-maven-3.8.6"/>
    <property name="java.vm.name" value="Java HotSpot(TM) Client VM"/>
    <property name="BUILD_NUMBER" value="8"/>
    <property name="BASE" value="C:\Users\<USER>\.jenkins"/>
    <property name="JENKINS_URL" value="http://localhost:8080/"/>
    <property name="PROCESSOR_REVISION" value="8c01"/>
    <property name="GIT_BRANCH" value="origin/master"/>
    <property name="ProgramW6432" value="C:\Program Files"/>
    <property name="windir" value="C:\Windows"/>
    <property name="GIT_COMMIT" value="fdf278643c41e74d7aa0da01774c5cb1ce435471"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="SystemDrive" value="C:"/>
    <property name="maven3.interceptor" value="C:\Users\<USER>\.jenkins\plugins\maven-plugin\WEB-INF\lib\maven35-interceptor-1.13.jar"/>
    <property name="WORKSPACE_TMP" value="C:\Users\<USER>\.jenkins\workspace\GCash_Api_Automation@tmp"/>
    <property name="WORKSPACE" value="C:\Users\<USER>\.jenkins\workspace\GCash_Api_Automation"/>
    <property name="maven3.interceptor.common" value="C:\Users\<USER>\.jenkins\plugins\maven-plugin\WEB-INF\lib\maven3-interceptor-commons-1.13.jar"/>
    <property name="WINSW_EXECUTABLE" value="C:\Users\<USER>\.jenkins\jenkins.exe"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="PSModulePath" value="C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules"/>
    <property name="DriverData" value="C:\Windows\System32\Drivers\DriverData"/>
    <property name="GIT_URL" value="https://github.com/pulkitkr/GCash_API_Project.git"/>
  </properties>
  <testcase name="GCashToken" classname="com.GCash_GGivesScripts.GCASHScripts" time="13.727"/>
  <testcase name="GCashClientId" classname="com.GCash_GGivesScripts.GCASHScripts" time="18.034"/>
  <testcase name="GCashEmptyId" classname="com.GCash_GGivesScripts.GCASHScripts" time="16.223"/>
  <testcase name="GCashSecretId" classname="com.GCash_GGivesScripts.GCASHScripts" time="13.78"/>
  <testcase name="GCashInvalidSecret" classname="com.GCash_GGivesScripts.GCASHScripts" time="10.988"/>
  <testcase name="GCashGrant" classname="com.GCash_GGivesScripts.GCASHScripts" time="10.411"/>
  <testcase name="GCashEmptyGrant" classname="com.GCash_GGivesScripts.GCASHScripts" time="11.936"/>
</testsuite>