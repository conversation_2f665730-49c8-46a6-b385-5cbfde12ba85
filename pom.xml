<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>GCASH_TAF</groupId>
	<artifactId>GCASH_TAF</artifactId>
	<version>1.0-SNAPSHOT</version>
	<repositories>
		<repository>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>fail</checksumPolicy>
			</releases>
			<id>Experitest.repo1</id>
			<name>YourName</name>
			<url>https://cloud.experitest.com/repo/</url>
			<layout>default</layout>
		</repository>
		<repository>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>bintray-epam-reportportal</id>
			<name>bintray</name>
			<url>http://dl.bintray.com/epam/reportportal</url>
		</repository>
	</repositories>
	<packaging>jar</packaging>
	<name>SeetestAutomation</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<m2e.disableTestClasspathFlag>true</m2e.disableTestClasspathFlag>
	</properties>


	<dependencies>
		<dependency>
			<groupId>io.appium</groupId>
			<artifactId>java-client</artifactId>
			<version>7.2.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/io.appium/java-client -->

		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>3.141.59</version>
		</dependency>

		<dependency>
			<groupId>io.github.bonigarcia</groupId>
			<artifactId>webdrivermanager</artifactId>
			<version>3.0.0</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/com.mashape.unirest/unirest-java -->
		<dependency>
			<groupId>com.mashape.unirest</groupId>
			<artifactId>unirest-java</artifactId>
			<version>1.4.9</version>
		</dependency>

		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>7.1.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.xmlbeans/xmlbeans -->
		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>3.1.0</version>
		</dependency>

		<!-- <dependency> <groupId>org.testng</groupId> <artifactId>testng</artifactId> 
			<version>6.9.10</version> </dependency> -->

		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-jdk14</artifactId>
			<version>1.7.25</version>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>23.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.aventstack/extentreports -->
		<dependency>
			<groupId>com.aventstack</groupId>
			<artifactId>extentreports</artifactId>
			<version>4.0.9</version>
		</dependency>

		<!-- <dependency>
			<groupId>com.jayway.restassured</groupId>
			<artifactId>rest-assured</artifactId>
			<version>2.3.4</version>
		</dependency> -->
		<dependency>
			<groupId>io.rest-assured</groupId>
			<artifactId>rest-assured</artifactId>
			<version>4.3.0</version>
			<!--<scope>test</scope> -->
		</dependency>
		
		<dependency>
			<groupId>io.rest-assured</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>5.2.0</version>
		</dependency>
		
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.8.1</version>
		</dependency>

		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.3.5</version>
		</dependency>

		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20180813</version>
		</dependency>

		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>3.3</version>
		</dependency>

		<dependency>
			<groupId>net.sourceforge.javacsv</groupId>
			<artifactId>javacsv</artifactId>
			<version>2.0</version>
		</dependency>

		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>javax.mail-api</artifactId>
			<version>1.5.5</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.11</version>
		</dependency>

		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis</artifactId>
			<version>1.4</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/com.sun.mail/javax.mail -->
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-validator/commons-validator -->
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.6</version>
		</dependency>

		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.applitools/eyes-selenium-java -->
		<dependency>
			<groupId>com.applitools</groupId>
			<artifactId>eyes-images-java3</artifactId>
			<version>3.158.9</version>
		</dependency>
		<!-- Report portal Dependency -->
		<dependency>
			<groupId>com.epam.reportportal</groupId>
			<artifactId>agent-java-testng</artifactId>
			<version>5.0.7</version>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.3</version>
		</dependency>

		<dependency>
			<groupId>com.epam.reportportal</groupId>
			<artifactId>logger-java-log4j</artifactId>
			<version>5.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.13.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.13.3</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/com.googlecode.json-simple/json-simple -->
		<dependency>
		    <groupId>com.googlecode.json-simple</groupId>
		    <artifactId>json-simple</artifactId>
		    <version>1.1</version>
		</dependency>	

	    <dependency>
		    <groupId>com.github.json-template</groupId>
		    <artifactId>jsontemplate</artifactId>
		    <version>0.2.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.rest-assured/json-schema-validator -->
		<dependency>
			<groupId>io.rest-assured</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>5.2.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.hamcrest/hamcrest-all -->
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-all</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.4</version>
		</dependency>


		<!-- <dependency> <groupId>com.epam.reportportal</groupId> <artifactId>agent-java-testng</artifactId> 
			<version>5.0.8</version> </dependency> TODO Leave only one dependency, depends 
			on what logger you use: <dependency> <groupId>com.epam.reportportal</groupId> 
			<artifactId>logger-java-logback</artifactId> <version>5.0.3</version> </dependency> 
			<dependency> <groupId>com.epam.reportportal</groupId> <artifactId>logger-java-log4j</artifactId> 
			<version>5.0.3</version> </dependency> -->

		<!-- <dependency> <groupId>ch.qos.logback</groupId> <artifactId>logback-classic</artifactId> 
			<version>1.2.3</version> </dependency> -->

		<!-- <dependency> <groupId>org.apache.logging.log4j</groupId> <artifactId>log4j-api</artifactId> 
			<version>2.12.1</version> </dependency> <dependency> <groupId>org.apache.logging.log4j</groupId> 
			<artifactId>log4j-core</artifactId> <version>2.12.1</version> </dependency> -->

		<!-- <dependency> <groupId>org.slf4j</groupId> <artifactId>slf4j-api</artifactId> 
			<version>1.7.28</version> </dependency> <dependency> <groupId>org.apache.logging.log4j</groupId> 
			<artifactId>log4j-slf4j-impl</artifactId> <version>2.12.1</version> </dependency> -->

		<!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-slf4j-impl -->
		<!-- <dependency> <groupId>org.apache.logging.log4j</groupId> <artifactId>log4j-slf4j-impl</artifactId> 
			<version>2.13.3</version> </dependency> -->

	</dependencies>
	<build>
		<plugins>
			<!-- Compiler plug-in -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.6.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<!-- Below plug-in is used to execute tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.20</version>
				<configuration>
					<useSystemClassLoader>false</useSystemClassLoader>
					<systemPropertyVariables>
					<!--<UserType>${UserType}</UserType>
						<BrowserType>${BrowserType}</BrowserType>
						<url>${url}</url>
						 <runMode>${runMode}</runMode> 
						 <runModule>${runModule}</runModule>
						 <BuildVersionChoice>${BuildVersionChoice}</BuildVersionChoice> 
						 <BuildVersion>${BuildVersion}</BuildVersion> -->
					</systemPropertyVariables>
					<suiteXmlFiles>
						<!-- TestNG suite XML files -->
						<!-- <suiteXmlFile>${rp.launch}.xml</suiteXmlFile> -->
						<suiteXmlFile>Gcash.xml</suiteXmlFile>
						<!-- <suiteXmlFile>${suiteXmlFile}.xml</suiteXmlFile> -->
						<!-- <suiteXmlFile>pwaAndroidHLS.xml</suiteXmlFile> -->
					</suiteXmlFiles>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>