<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript" tests="8" failures="1" timestamp="6 May 2020 08:53:42 GMT" time="3174.862" errors="0">
  <testcase name="PWAConsumptionsScreen" time="462.988" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWAOnboarding" time="142.876" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWAUICheck" time="131.677" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Pause icon  is displayed expected [true] but found [false],
	ElementLive TV Toggle  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Pause icon  is displayed expected [true] but found [false],
	ElementLive TV Toggle  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:235)
	at com.business.zee.Zee5PWABusinessLogic.verifyLiveTvAndChannelGuideScreen(Zee5PWABusinessLogic.java:2360)
	at com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAUICheck(AndroidPWAIntegratedScript.java:98)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWAUICheck -->
  <testcase name="PWASearch" time="154.372" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWACarousel" time="1022.040" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWASubscription" time="725.692" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWALandingScreen" time="63.060" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWAPlayer" time="472.157" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
</testsuite> <!-- com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript -->
