<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="DESKTOP-UFUPRO6" failures="5" tests="6" name="TvZee5TvperformanceScripts" time="563.45" errors="0" timestamp="2021-12-06T13:43:15 GMT">
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts" name="appLaunchPerformance" time="4.66">
    <failure type="java.lang.NullPointerException" message="Cannot invoke &amp;quot;String.trim()&amp;quot; because the return value of &amp;quot;java.io.BufferedReader.readLine()&amp;quot; is null">
      <![CDATA[java.lang.NullPointerException: Cannot invoke "String.trim()" because the return value of "java.io.BufferedReader.readLine()" is null
at com.business.zee.Zee5TvBusinessLogic.Memory_UsagePerformance(Zee5TvBusinessLogic.java:11400)
at com.business.zee.Zee5TvBusinessLogic.appLaunch(Zee5TvBusinessLogic.java:11669)
at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.appLaunchPerformance(TvZee5TvperformanceScripts.java:20)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames]]>
    </failure>
  </testcase> <!-- appLaunchPerformance -->
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts" name="loginPerformance" time="21.87">
    <skipped/>
  </testcase> <!-- loginPerformance -->
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts" name="loginPerformance" time="21.872">
    <failure type="java.lang.IllegalArgumentException" message="nodeName cannot be null or empty">
      <![CDATA[java.lang.IllegalArgumentException: nodeName cannot be null or empty
at com.aventstack.extentreports.ExtentTest.createNode(ExtentTest.java:166)
at com.aventstack.extentreports.ExtentTest.createNode(ExtentTest.java:274)
at com.extent.ExtentReporter.HeaderChildNode(ExtentReporter.java:247)
at com.extent.ExtentReporter.onTestSkipped(ExtentReporter.java:237)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 24 stack frames]]>
    </failure>
  </testcase> <!-- loginPerformance -->
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts" name="navigationPerformance" time="60.368">
    <failure type="java.lang.NullPointerException" message="Cannot invoke &amp;quot;String.trim()&amp;quot; because the return value of &amp;quot;java.io.BufferedReader.readLine()&amp;quot; is null">
      <![CDATA[java.lang.NullPointerException: Cannot invoke "String.trim()" because the return value of "java.io.BufferedReader.readLine()" is null
at com.business.zee.Zee5TvBusinessLogic.Memory_UsagePerformance(Zee5TvBusinessLogic.java:11400)
at com.business.zee.Zee5TvBusinessLogic.SelectTopNavigationTab_Timer(Zee5TvBusinessLogic.java:11913)
at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.navigationPerformance(TvZee5TvperformanceScripts.java:32)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames]]>
    </failure>
  </testcase> <!-- navigationPerformance -->
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts" name="playbackperformanace" time="33.14">
    <failure type="org.openqa.selenium.NoSuchElementException" message="no such element (An element could not be located on the page using the given search parameters (XPATH=&amp;apos;//*[@id=&amp;apos;search_icon_menu&amp;apos;]&amp;apos;))  (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: &amp;apos;3.141.59&amp;apos;, revision: &amp;apos;e82be7d358&amp;apos;, time: &amp;apos;2018-11-14T08:17:03&amp;apos;
System info: host: &amp;apos;DESKTOP-UFUPRO6&amp;apos;, ip: &amp;apos;*********&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;17.0.1&amp;apos;
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.zee5.player.activities...., appBuildVersion: , appPackage: com.graymatrix.did, appReleaseVersion: , appiumVersion: 1.8.0, applicationClearData: false, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, commandTimeouts: 120000, desired: {appActivity: com.zee5.player.activities...., appPackage: com.graymatrix.did, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: STB, device.majorVersion: 9, device.manufacture: skyworth, device.model: Y Series, device.name: Y Series, device.os: Android, device.screenSize: 1280x720, device.serialNumber: *********:5555, device.version: 9, deviceName: Android, deviceUDID: *********:5555, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, installOnlyForUpdate: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudio-r..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 12/06/21 01:35 PM, udid: *********:5555, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: b274e883-8356-4b96-92d1-be2b3f6ba051
*** Element info: {Using=xpath, value=//*[@id=&amp;apos;search_icon_menu&amp;apos;]}">
      <![CDATA[org.openqa.selenium.NoSuchElementException: no such element (An element could not be located on the page using the given search parameters (XPATH='//*[@id='search_icon_menu']'))  (WARNING: The server did not provide any stacktrace information)
Command duration or timeout: 0 milliseconds
For documentation on this error, please visit: https://www.seleniumhq.org/exceptions/no_such_element.html
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:17:03'
System info: host: 'DESKTOP-UFUPRO6', ip: '*********', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '17.0.1'
Driver info: io.appium.java_client.android.AndroidDriver
Capabilities {appActivity: com.zee5.player.activities...., appBuildVersion: , appPackage: com.graymatrix.did, appReleaseVersion: , appiumVersion: 1.8.0, applicationClearData: false, autoAcceptAlerts: true, autoDismissAlerts: false, autoGrantPermissions: false, autoWebview: false, automationName: uiautomator2, commandTimeouts: 120000, desired: {appActivity: com.zee5.player.activities...., appPackage: com.graymatrix.did, autoAcceptAlerts: true, automationName: uiautomator2, deviceName: Android, fullReset: false, newCommandTimeout: 300, platformName: Android}, device.category: STB, device.majorVersion: 9, device.manufacture: skyworth, device.model: Y Series, device.name: Y Series, device.os: Android, device.screenSize: 1280x720, device.serialNumber: *********:5555, device.version: 9, deviceName: Android, deviceUDID: *********:5555, dontGoHomeOnQuit: false, dontStopAppOnReset: false, fullReset: false, install.only.for.update: false, installOnlyForUpdate: false, instrumentApp: false, javascriptEnabled: true, keystorePath: ~/.android/debug.keystore, locationServicesAuthorized: false, newCommandTimeout: 300, newSessionWaitTimeout: 600, noReset: false, platform: ANDROID, platformName: Android, projectName: , reportDirectory: reports, reportFormat: xml, reportUrl: C:\Users\<USER>\appiumstudio-r..., reservationDuration: 240, takeScreenshots: true, test.type: Mobile, testName: mobile test 12/06/21 01:35 PM, udid: *********:5555, useKeystore: false, waitForDeviceTimeout: 120000}
Session ID: b274e883-8356-4b96-92d1-be2b3f6ba051
*** Element info: {Using=xpath, value=//*[@id='search_icon_menu']}
at org.openqa.selenium.remote.ErrorHandler.createThrowable(ErrorHandler.java:214)
at org.openqa.selenium.remote.ErrorHandler.throwIfResponseFailed(ErrorHandler.java:166)
at org.openqa.selenium.remote.http.JsonHttpResponseCodec.reconstructValue(JsonHttpResponseCodec.java:40)
at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:80)
at org.openqa.selenium.remote.http.AbstractHttpResponseCodec.decode(AbstractHttpResponseCodec.java:44)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:158)
at io.appium.java_client.remote.AppiumCommandExecutor.execute(AppiumCommandExecutor.java:239)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:552)
at io.appium.java_client.DefaultGenericMobileDriver.execute(DefaultGenericMobileDriver.java:41)
at io.appium.java_client.AppiumDriver.execute(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.execute(AndroidDriver.java:1)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:323)
at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:61)
at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
at org.openqa.selenium.remote.RemoteWebDriver.findElementByXPath(RemoteWebDriver.java:428)
at io.appium.java_client.DefaultGenericMobileDriver.findElementByXPath(DefaultGenericMobileDriver.java:151)
at io.appium.java_client.AppiumDriver.findElementByXPath(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.findElementByXPath(AndroidDriver.java:1)
at org.openqa.selenium.By$ByXPath.findElement(By.java:353)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:315)
at io.appium.java_client.DefaultGenericMobileDriver.findElement(DefaultGenericMobileDriver.java:57)
at io.appium.java_client.AppiumDriver.findElement(AppiumDriver.java:1)
at io.appium.java_client.android.AndroidDriver.findElement(AndroidDriver.java:1)
at com.utility.Utilities.TVgetAttributValue(Utilities.java:2197)
at com.business.zee.Zee5TvBusinessLogic.Performance_InitiateContentPlayback(Zee5TvBusinessLogic.java:12181)
at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.playbackperformanace(TvZee5TvperformanceScripts.java:38)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 32 stack frames]]>
    </failure>
  </testcase> <!-- playbackperformanace -->
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts" name="deeplinkperformanace" time="22.151">
    <failure type="java.lang.NullPointerException" message="Cannot invoke &amp;quot;org.openqa.selenium.WebDriver.manage()&amp;quot; because the return value of &amp;quot;com.utility.Utilities.getWebDriver()&amp;quot; is null">
      <![CDATA[java.lang.NullPointerException: Cannot invoke "org.openqa.selenium.WebDriver.manage()" because the return value of "com.utility.Utilities.getWebDriver()" is null
at com.utility.Utilities.verifyIsElementDisplayed(Utilities.java:475)
at com.business.zee.Zee5TvBusinessLogic.deepLink_Validation(Zee5TvBusinessLogic.java:12062)
at com.zee5.Zee5TvScripts.TvZee5TvperformanceScripts.deeplinkperformanace(TvZee5TvperformanceScripts.java:44)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
... Removed 27 stack frames]]>
    </failure>
  </testcase> <!-- deeplinkperformanace -->
</testsuite> <!-- TvZee5TvperformanceScripts -->
