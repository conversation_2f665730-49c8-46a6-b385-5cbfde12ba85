<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Web</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="d47cee">  <td>21/05/27 23:11:17</td>   <td>0</td> <td>&nbsp;</td><td title="&gt;&gt;VerifyURL.init()[pri:0, instance:com.VerifyURL.VerifyURL@43bd930a]">&gt;&gt;init</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@668210649</td>   <td></td> </tr>
<tr bgcolor="d47cee">  <td>21/05/27 23:11:43</td>   <td>26440</td> <td>&nbsp;</td><td title="&lt;&lt;VerifyURL.tearDown()[pri:0, instance:com.VerifyURL.VerifyURL@43bd930a]">&lt;&lt;tearDown</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@668210649</td>   <td></td> </tr>
<tr bgcolor="d47cee">  <td>21/05/27 23:11:29</td>   <td>12017</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="VerifyURL.verifyPageresponse()[pri:0, instance:com.VerifyURL.VerifyURL@43bd930a]">verifyPageresponse</td> 
  <td>main@668210649</td>   <td></td> </tr>
</table>
