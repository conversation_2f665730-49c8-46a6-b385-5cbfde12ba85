<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWASanityScripts.AndroidPWASanityScript" tests="2" failures="0" timestamp="20 Jul 2020 14:16:03 GMT" time="308.043" errors="1">
  <testcase name="Login" time="36.166" classname="com.zee5.PWASanityScripts.AndroidPWASanityScript"/>
  <testcase name="showsPage" time="271.877" classname="com.zee5.PWASanityScripts.AndroidPWASanityScript">
    <error type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
	at com.utility.Utilities.ScrollToTheElement(Utilities.java:1295)
	at com.business.zee.Zee5PWASanityAndroidBusinessLogic.landingPagesValidation(Zee5PWASanityAndroidBusinessLogic.java:9766)
	at com.business.zee.Zee5PWASanityAndroidBusinessLogic.ShowsValidation(Zee5PWASanityAndroidBusinessLogic.java:9609)
	at com.zee5.PWASanityScripts.AndroidPWASanityScript.showsPage(AndroidPWASanityScript.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- showsPage -->
</testsuite> <!-- com.zee5.PWASanityScripts.AndroidPWASanityScript -->
