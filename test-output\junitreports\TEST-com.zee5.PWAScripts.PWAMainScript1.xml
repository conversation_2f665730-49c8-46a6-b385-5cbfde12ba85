<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWAScripts.PWAMainScript1" tests="5" failures="5" timestamp="7 Apr 2020 15:36:26 GMT" time="120.122" errors="0">
  <testcase name="PWASearch" time="5.726" classname="com.zee5.PWAScripts.PWAMainScript1">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false],
	ElementLive TV Menu  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false],
	ElementLive TV Menu  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.fetchLiveContent(Zee5PWABusinessLogic.java:3134)
	at com.zee5.PWAScripts.PWAMainScript1.PWASearch(PWAMainScript1.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWASearch -->
  <testcase name="PWACarousel" time="0.900" classname="com.zee5.PWAScripts.PWAMainScript1">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false],
	ElementLive TV Menu  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false],
	ElementLive TV Menu  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.navigateToAnyScreen(Zee5PWABusinessLogic.java:2177)
	at com.business.zee.Zee5PWABusinessLogic.verifyPlayIconFunctionality(Zee5PWABusinessLogic.java:2137)
	at com.zee5.PWAScripts.PWAMainScript1.PWACarousel(PWAMainScript1.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWACarousel -->
  <testcase name="verifyConsumptionsScreen" time="0.693" classname="com.zee5.PWAScripts.PWAMainScript1">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false],
	ElementLive TV Menu  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false],
	ElementLive TV Menu  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1397)
	at com.zee5.PWAScripts.PWAMainScript1.verifyConsumptionsScreen(PWAMainScript1.java:73)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- verifyConsumptionsScreen -->
  <testcase name="PWAOnboarding" time="106.241" classname="com.zee5.PWAScripts.PWAMainScript1">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresent(Utilities.java:206)
	at com.business.zee.Zee5PWABusinessLogic.navigationToSignInFromCTAInPlayer(Zee5PWABusinessLogic.java:870)
	at com.business.zee.Zee5PWABusinessLogic.navigationToCTAInPlayerFromSearch(Zee5PWABusinessLogic.java:862)
	at com.business.zee.Zee5PWABusinessLogic.OnboardingScenario(Zee5PWABusinessLogic.java:534)
	at com.zee5.PWAScripts.PWAMainScript1.PWAOnboarding(PWAMainScript1.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWAOnboarding -->
  <testcase name="PWAUICheck" time="6.562" classname="com.zee5.PWAScripts.PWAMainScript1">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementSubscribe popup  is not visible expected [true] but found [false],
	ElementHamburger menu  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyUIofHomePage(Zee5PWABusinessLogic.java:1261)
	at com.zee5.PWAScripts.PWAMainScript1.PWAUICheck(PWAMainScript1.java:25)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWAUICheck -->
</testsuite> <!-- com.zee5.PWAScripts.PWAMainScript1 -->
