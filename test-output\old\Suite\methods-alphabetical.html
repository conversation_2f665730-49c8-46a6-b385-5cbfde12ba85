<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="d47cee">  <td>21/05/27 22:49:19</td>   <td>0</td> <td>&nbsp;</td><td title="&gt;&gt;VerifyURL.init()[pri:0, instance:com.VerifyURL.VerifyURL@569cfc36]">&gt;&gt;init</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@265119009</td>   <td></td> </tr>
<tr bgcolor="d47cee">  <td>21/05/27 22:49:22</td>   <td>2333</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="VerifyURL.verifyPageresponse()[pri:0, instance:com.VerifyURL.VerifyURL@569cfc36]">verifyPageresponse</td> 
  <td>main@265119009</td>   <td></td> </tr>
</table>
