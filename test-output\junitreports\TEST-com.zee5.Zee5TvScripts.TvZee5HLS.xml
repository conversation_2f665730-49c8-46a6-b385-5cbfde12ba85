<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="DESKTOP-UFUPRO6" failures="0" tests="12" name="com.zee5.Zee5TvScripts.TvZee5HLS" time="0.000" errors="6" timestamp="2021-12-06T15:18:15 GMT" skipped="6">
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="TvLogin" time="0.000">
    <error message="
Parameter &#039;userType&#039; is required by @Test on method TvLogin but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml" type="org.testng.TestNGException">
      <![CDATA[org.testng.TestNGException: 
Parameter 'userType' is required by @Test on method TvLogin but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- TvLogin -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="searchScenarios" time="0.000">
    <error message="
Parameter &#039;userType&#039; is required by @Test on method searchScenarios but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml" type="org.testng.TestNGException">
      <![CDATA[org.testng.TestNGException: 
Parameter 'userType' is required by @Test on method searchScenarios but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- searchScenarios -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="playback" time="0.000">
    <error message="
Parameter &#039;userType&#039; is required by @Test on method playback but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml" type="org.testng.TestNGException">
      <![CDATA[org.testng.TestNGException: 
Parameter 'userType' is required by @Test on method playback but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- playback -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="carousel" time="0.000">
    <error message="
Parameter &#039;userType&#039; is required by @Test on method carousel but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml" type="org.testng.TestNGException">
      <![CDATA[org.testng.TestNGException: 
Parameter 'userType' is required by @Test on method carousel but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- carousel -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="playerScenarios" time="0.000">
    <error message="
Parameter &#039;userType&#039; is required by @Test on method playerScenarios but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml" type="org.testng.TestNGException">
      <![CDATA[org.testng.TestNGException: 
Parameter 'userType' is required by @Test on method playerScenarios but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- playerScenarios -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="setting" time="0.000">
    <error message="
Parameter &#039;userType&#039; is required by @Test on method setting but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml" type="org.testng.TestNGException">
      <![CDATA[org.testng.TestNGException: 
Parameter 'userType' is required by @Test on method setting but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse-749641344\testng-customsuite.xml
at org.testng.internal.Parameters.createParams(Parameters.java:272)
at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
at org.testng.internal.Parameters.createParameters(Parameters.java:704)
at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
at org.testng.TestRunner.privateRun(TestRunner.java:766)
at org.testng.TestRunner.run(TestRunner.java:587)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
at org.testng.SuiteRunner.run(SuiteRunner.java:286)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
at org.testng.TestNG.runSuites(TestNG.java:1039)
at org.testng.TestNG.run(TestNG.java:1007)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </error>
  </testcase> <!-- setting -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="subscription" time="0.000">
    <skipped/>
  </testcase> <!-- subscription -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="liveTV" time="0.000">
    <skipped/>
  </testcase> <!-- liveTV -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="language" time="0.000">
    <skipped/>
  </testcase> <!-- language -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="headerSection" time="0.000">
    <skipped/>
  </testcase> <!-- headerSection -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="staticPage" time="0.000">
    <skipped/>
  </testcase> <!-- staticPage -->
  <system-out/>
  <testcase classname="com.zee5.Zee5TvScripts.TvZee5HLS" name="contactUS" time="0.000">
    <skipped/>
  </testcase> <!-- contactUS -->
  <system-out/>
</testsuite> <!-- com.zee5.Zee5TvScripts.TvZee5HLS -->
