<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWAScripts.PWACarouselValidations" tests="6" failures="5" timestamp="8 Apr 2020 04:44:57 GMT" time="0.035" errors="0">
  <testcase name="verifyMetadataOnCarousel" time="0.008" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :News  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :News  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.navigateToAnyScreen(Zee5PWABusinessLogic.java:2177)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetadataOnCarousel(Zee5PWABusinessLogic.java:3046)
	at com.zee5.PWAScripts.PWACarouselValidations.verifyMetadataOnCarousel(PWACarouselValidations.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- verifyMetadataOnCarousel -->
  <testcase name="appLaunch" time="0.008" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="verifyAutoroatingFunct" time="0.000" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.navigateToAnyScreen(Zee5PWABusinessLogic.java:2177)
	at com.business.zee.Zee5PWABusinessLogic.verifyAutoroatingOnCarousel(Zee5PWABusinessLogic.java:2083)
	at com.zee5.PWAScripts.PWACarouselValidations.verifyAutoroatingFunct(PWACarouselValidations.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- verifyAutoroatingFunct -->
  <testcase name="verifyPremiumIcon" time="0.008" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.navigateToAnyScreen(Zee5PWABusinessLogic.java:2177)
	at com.business.zee.Zee5PWABusinessLogic.verifyPremiumIconFunctionality(Zee5PWABusinessLogic.java:2224)
	at com.zee5.PWAScripts.PWACarouselValidations.verifyPremiumIcon(PWACarouselValidations.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- verifyPremiumIcon -->
  <testcase name="verifyLaftAndRightFunct" time="0.009" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :News  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :Home  is not visible expected [true] but found [false],
	Element selected screen :News  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.navigateToAnyScreen(Zee5PWABusinessLogic.java:2177)
	at com.business.zee.Zee5PWABusinessLogic.verifyLeftRightFunctionality(Zee5PWABusinessLogic.java:2267)
	at com.zee5.PWAScripts.PWACarouselValidations.verifyLaftAndRightFunct(PWACarouselValidations.java:57)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- verifyLaftAndRightFunct -->
  <testcase name="verifyPlayIconFunctionality" time="0.002" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Element selected screen :Home  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.navigateToAnyScreen(Zee5PWABusinessLogic.java:2177)
	at com.business.zee.Zee5PWABusinessLogic.verifyPlayIconFunctionality(Zee5PWABusinessLogic.java:2137)
	at com.zee5.PWAScripts.PWACarouselValidations.verifyPlayIconFunctionality(PWACarouselValidations.java:26)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- verifyPlayIconFunctionality -->
</testsuite> <!-- com.zee5.PWAScripts.PWACarouselValidations -->
