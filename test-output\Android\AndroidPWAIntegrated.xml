<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="AndroidPWAIntegrated" tests="8" failures="1" timestamp="6 May 2020 08:53:41 GMT" time="3304.457" errors="0">
  <testcase name="PWAOnboarding" time="142.876" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWAConsumptionsScreen" time="462.988" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWAPlayer" time="472.157" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWACarousel" time="1022.04" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWAUICheck" time="131.677" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Pause icon  is displayed expected [true] but found [false],
	ElementLive TV Toggle  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Pause icon  is displayed expected [true] but found [false],
	ElementLive TV Toggle  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:235)
	at com.business.zee.Zee5PWABusinessLogic.verifyLiveTvAndChannelGuideScreen(Zee5PWABusinessLogic.java:2360)
	at com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAUICheck(AndroidPWAIntegratedScript.java:98)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- PWAUICheck -->
  <testcase name="PWASearch" time="154.372" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWALandingScreen" time="63.06" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
  <testcase name="PWASubscription" time="725.692" classname="com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript"/>
</testsuite> <!-- AndroidPWAIntegrated -->
