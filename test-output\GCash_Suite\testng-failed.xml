<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Failed suite [GCash_Suite]" guice-stage="DEVELOPMENT">
  <parameter name="APIUrl" value="https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token"/>
  <parameter name="GSaveValidPhoneNumber" value="9010000105"/>
  <parameter name="GSaveValidOTP" value="000000"/>
  <parameter name="validphonenumber" value="9664087954"/>
  <parameter name="repaymentAMT" value="1.00"/>
  <parameter name="runMode" value="Suites"/>
  <parameter name="InvalidphoneNumber" value="9999999999"/>
  <parameter name="browserType" value="chrome"/>
  <parameter name="testExecutionKey" value="TC-21175"/>
  <parameter name="GGivesLoginValidOTP" value="000000"/>
  <parameter name="runModule" value="Suite"/>
  <parameter name="userType" value="Guest"/>
  <parameter name="amtPay" value="1.00"/>
  <parameter name="GGivesLoginInValidOTP" value="000001"/>
  <listeners>
    <listener class-name="com.extent.ExtentReporter"/>
  </listeners>
  <test thread-count="5" name="GCASH(failed)">
    <classes>
      <class name="com.GCash_GGivesScripts.GCASHScripts">
        <methods>
          <include name="Before"/>
          <include name="Allowpopup"/>
        </methods>
      </class> <!-- com.GCash_GGivesScripts.GCASHScripts -->
      <class name="com.GCash_GGivesScripts.TokenGCASH">
        <methods>
          <include name="EmptyClientSecret_TokenGCash"/>
          <include name="InvalidClientSecret_TokenGCash"/>
          <include name="EmptyGrantType_TokenGCash"/>
          <include name="InvalidClientId_TokenGCash"/>
          <include name="InvalidGrantType_TokenGCash"/>
          <include name="TokenGCash_200"/>
          <include name="EmptyClientId_TokenGCash"/>
        </methods>
      </class> <!-- com.GCash_GGivesScripts.TokenGCASH -->
    </classes>
  </test> <!-- GCASH(failed) -->
</suite> <!-- Failed suite [GCash_Suite] -->
