<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="com.zee5.PWASanityScripts.PWASanityMenuOrSetting" tests="1" failures="1" timestamp="28 Apr 2020 14:00:48 GMT" time="244.745" errors="0">
  <testcase name="PWAMenuOrSettingScenarios" time="244.745" classname="com.zee5.PWASanityScripts.PWASanityMenuOrSetting">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	ElementSet Lock Field  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	ElementSet Lock Field  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresent(Utilities.java:206)
	at com.business.zee.Zee5PWASanityBusinessLogic.parentControlFunctionality(Zee5PWASanityBusinessLogic.java:251)
	at com.business.zee.Zee5PWASanityBusinessLogic.MenuOrSettingScenarios(Zee5PWASanityBusinessLogic.java:389)
	at com.zee5.PWASanityScripts.PWASanityMenuOrSetting.PWAMenuOrSettingScenarios(PWASanityMenuOrSetting.java:22)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- PWAMenuOrSettingScenarios -->
</testsuite> <!-- com.zee5.PWASanityScripts.PWASanityMenuOrSetting -->
