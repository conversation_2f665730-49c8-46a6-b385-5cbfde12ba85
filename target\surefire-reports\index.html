<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>
    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <br/>
      <span class="top-banner-font-1">1 suite</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" class="collapse-all-link" title="Collapse/expand all the suites">
          <img class="collapse-all-icon" src="collapseall.gif">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" class="navigator-link" panel-name="suite-Android">
              <span class="suite-name border-passed">Android</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" class="navigator-link " panel-name="test-xml-Android">
                    <span>C:\Users\<USER>\Desktop\API_GCash\GCash_API_Project\Gcash.xml</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="testlist-Android">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="group-Android">
                    <span>0 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="times-Android">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="reporter-Android">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="ignored-methods-Android">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="chronological-Android">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">7 methods,   7 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-Android" class="hide-methods passed suite-Android"> (hide)</a> <!-- hide-methods passed suite-Android -->
                      <a href="#" panel-name="suite-Android" class="show-methods passed suite-Android"> (show)</a> <!-- show-methods passed suite-Android -->
                    </span>
                    <div class="method-list-content passed suite-Android">
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashClientId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashClientId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashEmptyGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashEmptyGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashEmptyId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashEmptyId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashInvalidSecret(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashInvalidSecret(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashSecretId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashSecretId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Android" title="com.GCash_GGivesScripts.GCASHScripts" hash-for-method="GCashToken(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">GCashToken(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-Android -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-Android" class="panel Android">
          <div class="suite-Android-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.GCash_GGivesScripts.GCASHScripts</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="GCashClientId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashClientId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashClientId</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="GCashEmptyGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashEmptyGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashEmptyGrant</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="GCashEmptyId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashEmptyId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashEmptyId</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="GCashGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashGrant</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="GCashInvalidSecret(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashInvalidSecret(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashInvalidSecret</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="GCashSecretId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashSecretId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashSecretId</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="GCashToken(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)">
                  </a> <!-- GCashToken(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token) -->
                  <span class="method-name">GCashToken</span>
                  <span class="parameters">(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Android-class-passed -->
        </div> <!-- panel Android -->
        <div panel-name="test-xml-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">C:\Users\<USER>\Desktop\API_GCash\GCash_API_Project\Gcash.xml</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;https://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite guice-stage=&quot;DEVELOPMENT&quot; verbose=&quot;0&quot; name=&quot;Android&quot;&gt;
  &lt;parameter name=&quot;testExecutionKey&quot; value=&quot;PP-42&quot;/&gt;
  &lt;parameter name=&quot;APIUrl&quot; value=&quot;https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token&quot;/&gt;
  &lt;parameter name=&quot;runModule&quot; value=&quot;Suite&quot;/&gt;
  &lt;parameter name=&quot;userType&quot; value=&quot;Guest&quot;/&gt;
  &lt;parameter name=&quot;runMode&quot; value=&quot;Suites&quot;/&gt;
  &lt;parameter name=&quot;browserType&quot; value=&quot;chrome&quot;/&gt;
  &lt;listeners&gt;
    &lt;listener class-name=&quot;com.extent.ExtentReporter&quot;/&gt;
  &lt;/listeners&gt;
  &lt;test thread-count=&quot;5&quot; verbose=&quot;0&quot; name=&quot;GCASH&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.GCash_GGivesScripts.GCASHScripts&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- GCASH --&gt;
&lt;/suite&gt; &lt;!-- Android --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for Android</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">GCASH (1 class)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for Android</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for Android</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_Android');
function tableData_Android() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(7);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'GCashToken')
data.setCell(0, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(0, 3, 3188);
data.setCell(1, 0, 1)
data.setCell(1, 1, 'GCashEmptyId')
data.setCell(1, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(1, 3, 783);
data.setCell(2, 0, 2)
data.setCell(2, 1, 'GCashGrant')
data.setCell(2, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(2, 3, 742);
data.setCell(3, 0, 3)
data.setCell(3, 1, 'GCashClientId')
data.setCell(3, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(3, 3, 682);
data.setCell(4, 0, 4)
data.setCell(4, 1, 'GCashSecretId')
data.setCell(4, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(4, 3, 629);
data.setCell(5, 0, 5)
data.setCell(5, 1, 'GCashInvalidSecret')
data.setCell(5, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(5, 3, 566);
data.setCell(6, 0, 6)
data.setCell(6, 1, 'GCashEmptyGrant')
data.setCell(6, 2, 'com.GCash_GGivesScripts.GCASHScripts')
data.setCell(6, 3, 492);
window.suiteTableData['Android']= { tableData: data, tableDiv: 'times-div-Android'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 7 seconds</span>
              <div id="times-div-Android">
              </div> <!-- times-div-Android -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for Android</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">0 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-Android" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">com.GCash_GGivesScripts.GCASHScripts</div> <!-- chronological-class-name -->
              <div class="configuration-test before">
                <span class="method-name">Before</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-test before -->
              <div class="test-method">
                <span class="method-name">GCashToken(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">19426 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">GCashClientId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">33614 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">GCashEmptyId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">51661 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">GCashSecretId(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">67894 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">GCashInvalidSecret(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">81684 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">GCashGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">92683 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">GCashEmptyGrant(https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token)</span>
                <span class="method-start">103106 ms</span>
              </div> <!-- test-method -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
</html>
