<html>
<head>
<title>TestNG:  AndroidPWAIntegrated</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>AndroidPWAIntegrated</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>7/1/0</td>
</tr><tr>
<td>Started on:</td><td>Wed May 06 13:28:37 IST 2020</td>
</tr>
<tr><td>Total time:</td><td>3304 seconds (3304457 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAUICheck()'><b>PWAUICheck</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: http://newpwa.zee5.com/</td>
<td><div><pre>java.lang.AssertionError: The following asserts failed:
	Pause icon  is displayed expected [true] but found [false],
	ElementLive TV Toggle  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:235)
	at com.business.zee.Zee5PWABusinessLogic.verifyLiveTvAndChannelGuideScreen(Zee5PWABusinessLogic.java:2360)
	at com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAUICheck(AndroidPWAIntegratedScript.java:98)
... Removed 25 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1435477578", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1435477578'><pre>java.lang.AssertionError: The following asserts failed:
	Pause icon  is displayed expected [true] but found [false],
	ElementLive TV Toggle  is not visible expected [true] but found [false]
	at org.testng.asserts.SoftAssert.assertAll(SoftAssert.java:43)
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:235)
	at com.business.zee.Zee5PWABusinessLogic.verifyLiveTvAndChannelGuideScreen(Zee5PWABusinessLogic.java:2360)
	at com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAUICheck(AndroidPWAIntegratedScript.java:98)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:86)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:643)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:820)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:1128)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:129)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:112)
	at org.testng.TestRunner.privateRun(TestRunner.java:782)
	at org.testng.TestRunner.run(TestRunner.java:632)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:366)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:361)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:319)
	at org.testng.SuiteRunner.run(SuiteRunner.java:268)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:86)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1244)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1169)
	at org.testng.TestNG.run(TestNG.java:1064)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>131</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWACarousel()'><b>PWACarousel</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: Guest, http://newpwa.zee5.com/</td>
<td></td>
<td>1022</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAConsumptionsScreen()'><b>PWAConsumptionsScreen</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: chrome, http://newpwa.zee5.com/, Guest, 1619, Mahira accuses Preeta of trying to kill Mahesh, Kundali Bhagya, Prema Baraha, Phuljhadi Wala Rocket, Jodi Hakki, Gattimela</td>
<td></td>
<td>462</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWALandingScreen()'><b>PWALandingScreen</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: Guest, http://newpwa.zee5.com/</td>
<td></td>
<td>63</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAOnboarding()'><b>PWAOnboarding</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: Guest</td>
<td></td>
<td>142</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWAPlayer()'><b>PWAPlayer</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: Guest, http://newpwa.zee5.com/</td>
<td></td>
<td>472</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWASearch()'><b>PWASearch</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: Kamali, http://newpwa.zee5.com/</td>
<td></td>
<td>154</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
<tr>
<td title='com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript.PWASubscription()'><b>PWASubscription</b><br>Test class: com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript<br>Parameters: Guest, http://newpwa.zee5.com/</td>
<td></td>
<td>725</td>
<td>com.zee5.PWASmokeScripts.AndroidPWAIntegratedScript@617faa95</td></tr>
</table><p>
</body>
</html>