<html><head><title>testng.xml for Android</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;name="Android"&nbsp;guice-stage="DEVELOPMENT"&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="testExecutionKey"&nbsp;value="PP-42"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="APIUrl"&nbsp;value="https://api.lab.mynt.xyz/telco-score/trustingsocial/oauth2/token"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="runModule"&nbsp;value="Suite"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="userType"&nbsp;value="Guest"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="runMode"&nbsp;value="Suites"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="browserType"&nbsp;value="chrome"/&gt;
<br/>&nbsp;&nbsp;&lt;listeners&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;listener&nbsp;class-name="com.extent.ExtentReporter"/&gt;
<br/>&nbsp;&nbsp;&lt;/listeners&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="5"&nbsp;name="GCASH"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.GCash_GGivesScripts.GCASHScripts"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;GCASH&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;Android&nbsp;--&gt;
<br/></tt></body></html>