<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="PWACarouselValidations" tests="6" failures="1" timestamp="7 Apr 2020 07:12:45 GMT" time="453.083" errors="0">
  <testcase name="appLaunch" time="14.348" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="verifyPlayIconFunctionality" time="128.056" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="verifyPremiumIcon" time="69.961" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="verifyAutoroatingFunct" time="103.145" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="verifyLaftAndRightFunct" time="20.934" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="verifyMetadataOnCarousel" time="27.087" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Elementcarousel for :Shows  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Player control containing screen  is displayed expected [true] but found [false],
	Elementcarousel for :Shows  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresent(Utilities.java:206)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetadataOnCarousel(Zee5PWABusinessLogic.java:3034)
	at com.zee5.PWAScripts.PWACarouselValidations.verifyMetadataOnCarousel(PWACarouselValidations.java:62)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifyMetadataOnCarousel -->
</testsuite> <!-- PWACarouselValidations -->
