<html>
<head>
<title>TestNG:  Default test</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Default test</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/7/0</td>
</tr><tr>
<td>Started on:</td><td>Tue Sep 20 13:18:45 IST 2022</td>
</tr>
<tr><td>Total time:</td><td>0 seconds (40 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.EmptyClientId_TokenGCash()'><b>EmptyClientId_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method EmptyClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace1251788491", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1251788491'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method EmptyClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.EmptyClientSecret_TokenGCash()'><b>EmptyClientSecret_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method EmptyClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace1895479349", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1895479349'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method EmptyClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.EmptyGrantType_TokenGCash()'><b>EmptyGrantType_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method EmptyGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace1648458262", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1648458262'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method EmptyGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.InvalidClientId_TokenGCash()'><b>InvalidClientId_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method InvalidClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace650013863", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace650013863'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method InvalidClientId_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.InvalidClientSecret_TokenGCash()'><b>InvalidClientSecret_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method InvalidClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace123337428", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace123337428'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method InvalidClientSecret_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.InvalidGrantType_TokenGCash()'><b>InvalidGrantType_TokenGCash</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method InvalidGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace1397333381", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1397333381'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method InvalidGrantType_TokenGCash but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
<tr>
<td title='com.GCash_GGivesScripts.TokenGCASH.TokenGCash_200()'><b>TokenGCash_200</b><br>Test class: com.GCash_GGivesScripts.TokenGCASH</td>
<td><div><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method TokenGCash_200 but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div><a href='#' onClick='toggleBox("stack-trace2016038911", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2016038911'><pre>org.testng.TestNGException: 
Parameter &apos;APIUrl&apos; is required by @Test on method TokenGCash_200 but has not been marked @Optional or defined
in C:\Users\<USER>\AppData\Local\Temp\testng-eclipse--626902169\testng-customsuite.xml
	at org.testng.internal.Parameters.createParams(Parameters.java:272)
	at org.testng.internal.Parameters.createParametersForMethod(Parameters.java:360)
	at org.testng.internal.Parameters.createParameters(Parameters.java:704)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:879)
	at org.testng.internal.Parameters.handleParameters(Parameters.java:744)
	at org.testng.internal.ParameterHandler.handleParameters(ParameterHandler.java:59)
	at org.testng.internal.ParameterHandler.createParameters(ParameterHandler.java:38)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:783)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:766)
	at org.testng.TestRunner.run(TestRunner.java:587)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:384)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:378)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:337)
	at org.testng.SuiteRunner.run(SuiteRunner.java:286)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1187)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1109)
	at org.testng.TestNG.runSuites(TestNG.java:1039)
	at org.testng.TestNG.run(TestNG.java:1007)
	at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
	at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
	at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
</pre></div></td>
<td>0</td>
<td>com.GCash_GGivesScripts.TokenGCASH@5f3b9c57</td></tr>
</table><p>
</body>
</html>