<html>
<head>
<title>TestNG:  ML_Wallet</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>ML_Wallet</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>7/0/0</td>
</tr><tr>
<td>Started on:</td><td>Wed Sep 07 13:06:41 IST 2022</td>
</tr>
<tr><td>Total time:</td><td>364 seconds (364432 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.Allowpopup()'><b>Allowpopup</b><br>Test class: com.MLWalletScripts.MLWalletScripts<br>Parameters: Guest</td>
<td></td>
<td>8</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.Login()'><b>Login</b><br>Test class: com.MLWalletScripts.MLWalletScripts<br>Parameters: Guest</td>
<td></td>
<td>95</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.gGivesDashboardPage()'><b>gGivesDashboardPage</b><br>Test class: com.MLWalletScripts.MLWalletScripts</td>
<td></td>
<td>137</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.gGivesDuesPage()'><b>gGivesDuesPage</b><br>Test class: com.MLWalletScripts.MLWalletScripts</td>
<td></td>
<td>0</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.gGivesHomePage()'><b>gGivesHomePage</b><br>Test class: com.MLWalletScripts.MLWalletScripts<br>Parameters: Guest</td>
<td></td>
<td>74</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.gGivesPaymentPage()'><b>gGivesPaymentPage</b><br>Test class: com.MLWalletScripts.MLWalletScripts</td>
<td></td>
<td>7</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
<tr>
<td title='com.MLWalletScripts.MLWalletScripts.gGivesViewPage()'><b>gGivesViewPage</b><br>Test class: com.MLWalletScripts.MLWalletScripts</td>
<td></td>
<td>3</td>
<td>com.MLWalletScripts.MLWalletScripts@69637b10</td></tr>
</table><p>
</body>
</html>