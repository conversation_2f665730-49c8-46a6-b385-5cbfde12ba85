<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="PWAContentDetailsPageVerificationScript" tests="12" failures="6" timestamp="6 Apr 2020 07:41:31 GMT" time="485.921" errors="0">
  <testcase name="@AfterTest tearDown" time="0.0" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.tearDown(Zee5PWABusinessLogic.java:160)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.tearDown(PWAContentDetailsPageVerificationScript.java:89)
... Removed 22 stack frames]]>
    </failure>
  </testcase> <!-- @AfterTest tearDown -->
  <testcase name="verifyConsumptionsScreen" time="132.426" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <skipped/>
  </testcase> <!-- verifyConsumptionsScreen -->
  <testcase name="verifyConsumptionsScreen" time="30.163" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyConsumptionsScreenTappingOnCard(Zee5PWABusinessLogic.java:1468)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyConsumptionsScreen(PWAContentDetailsPageVerificationScript.java:32)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifyConsumptionsScreen -->
  <testcase name="verifyWatchLatestEpisodeCTA" time="30.168" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <skipped/>
  </testcase> <!-- verifyWatchLatestEpisodeCTA -->
  <testcase name="verifyWatchLatestEpisodeCTA" time="30.111" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyWatchLatestEpisodeCTA(Zee5PWABusinessLogic.java:1508)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyWatchLatestEpisodeCTA(PWAContentDetailsPageVerificationScript.java:51)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifyWatchLatestEpisodeCTA -->
  <testcase name="verifyNoSubscriptionPopupForFreeContent" time="30.125" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <skipped/>
  </testcase> <!-- verifyNoSubscriptionPopupForFreeContent -->
  <testcase name="verifyNoSubscriptionPopupForFreeContent" time="30.112" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyNoSubscriptionPopupForFreeContent(Zee5PWABusinessLogic.java:1550)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyNoSubscriptionPopupForFreeContent(PWAContentDetailsPageVerificationScript.java:61)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifyNoSubscriptionPopupForFreeContent -->
  <testcase name="verifySubscriptionPopupForPremiumContent" time="30.1" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <skipped/>
  </testcase> <!-- verifySubscriptionPopupForPremiumContent -->
  <testcase name="verifySubscriptionPopupForPremiumContent" time="30.21" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifySubscriptionPopupForPremiumContent(Zee5PWABusinessLogic.java:1589)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifySubscriptionPopupForPremiumContent(PWAContentDetailsPageVerificationScript.java:70)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifySubscriptionPopupForPremiumContent -->
  <testcase name="verifyMetaDataInDetailsAndConsumption" time="30.138" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <skipped/>
  </testcase> <!-- verifyMetaDataInDetailsAndConsumption -->
  <testcase name="verifyMetaDataInDetailsAndConsumption" time="30.127" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyMetaDataInDetailsAndConsumption(Zee5PWABusinessLogic.java:1633)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyMetaDataInDetailsAndConsumption(PWAContentDetailsPageVerificationScript.java:78)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifyMetaDataInDetailsAndConsumption -->
  <testcase name="verifyShareInShowDetails" time="30.109" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <skipped/>
  </testcase> <!-- verifyShareInShowDetails -->
  <testcase name="verifyShareInShowDetails" time="30.106" classname="com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript">
    <failure type="java.lang.AssertionError" message="The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: The following asserts failed:
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false],
	Search icon  is displayed expected [true] but found [false],
	ElementSearch icon  is not visible expected [true] but found [false]
	at com.utility.Utilities.verifyElementPresentAndClick(Utilities.java:251)
	at com.business.zee.Zee5PWABusinessLogic.verifyShareInShowDetails(Zee5PWABusinessLogic.java:1704)
	at com.zee5.PWAScripts.PWAContentDetailsPageVerificationScript.verifyShareInShowDetails(PWAContentDetailsPageVerificationScript.java:84)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- verifyShareInShowDetails -->
</testsuite> <!-- PWAContentDetailsPageVerificationScript -->
