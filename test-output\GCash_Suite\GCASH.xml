<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="DESKTOP-077G3EM" failures="0" tests="8" name="GCASH" time="12.832" errors="0" timestamp="2022-09-19T21:49:04 IST">
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="@BeforeTest Before" time="2.801">
    <skipped/>
  </testcase> <!-- @BeforeTest Before -->
  <testcase classname="com.GCash_GGivesScripts.GCASHScripts" name="Allowpopup" time="0.0">
    <skipped/>
  </testcase> <!-- Allowpopup -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="EmptyClientId_TokenGCash" time="0.0">
    <skipped/>
  </testcase> <!-- EmptyClientId_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="EmptyClientSecret_TokenGCash" time="0.0">
    <skipped/>
  </testcase> <!-- EmptyClientSecret_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="EmptyGrantType_TokenGCash" time="0.0">
    <skipped/>
  </testcase> <!-- EmptyGrantType_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="InvalidClientId_TokenGCash" time="0.0">
    <skipped/>
  </testcase> <!-- InvalidClientId_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="InvalidClientSecret_TokenGCash" time="0.0">
    <skipped/>
  </testcase> <!-- InvalidClientSecret_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="InvalidGrantType_TokenGCash" time="0.0">
    <skipped/>
  </testcase> <!-- InvalidGrantType_TokenGCash -->
  <testcase classname="com.GCash_GGivesScripts.TokenGCASH" name="TokenGCash_200" time="0.0">
    <skipped/>
  </testcase> <!-- TokenGCash_200 -->
</testsuite> <!-- GCASH -->
