<html><head><title>testng.xml for TV</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;thread-count="1"&nbsp;name="TV"&nbsp;guice-stage="DEVELOPMENT"&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="testExecutionKey"&nbsp;value="TC-21175"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="runModule"&nbsp;value="Suite"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="userType"&nbsp;value="Guest"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="runMode"&nbsp;value="Suites"/&gt;
<br/>&nbsp;&nbsp;&lt;parameter&nbsp;name="browserType"&nbsp;value="chrome"/&gt;
<br/>&nbsp;&nbsp;&lt;listeners&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;listener&nbsp;class-name="com.extent.ExtentReporter"/&gt;
<br/>&nbsp;&nbsp;&lt;/listeners&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="1"&nbsp;name="ML_Wallet"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.MLWalletScripts.MLWalletScripts"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;ML_Wallet&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;TV&nbsp;--&gt;
<br/></tt></body></html>