<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="LAPTOP-NRJMSC7T" name="shreenidhiAndSushma" tests="2" failures="1" timestamp="6 Apr 2020 14:13:56 GMT" time="8.734" errors="0">
  <testcase name="@AfterTest tearDown" time="0.0" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
	at com.business.zee.Zee5PWABusinessLogic.tearDown(Zee5PWABusinessLogic.java:160)
	at com.zee5.PWAScripts.PWACarouselValidations.tearDown(PWACarouselValidations.java:72)
... Removed 22 stack frames]]>
    </failure>
  </testcase> <!-- @AfterTest tearDown -->
  <testcase name="appLaunch" time="0.036" classname="com.zee5.PWAScripts.PWACarouselValidations"/>
  <testcase name="appLaunch" time="0.058" classname="com.zee5.PWAScripts.PWACarouselValidations">
    <failure type="java.lang.NullPointerException" message="Source must not be null">
      <![CDATA[java.lang.NullPointerException: Source must not be null
	at org.apache.commons.io.FileUtils.checkFileRequirements(FileUtils.java:1377)
	at org.apache.commons.io.FileUtils.copyFile(FileUtils.java:1060)
	at org.apache.commons.io.FileUtils.copyFile(FileUtils.java:1028)
	at com.extent.ExtentReporter.screencapture(ExtentReporter.java:138)
	at com.extent.ExtentReporter.onTestSuccess(ExtentReporter.java:85)
... Removed 21 stack frames]]>
    </failure>
  </testcase> <!-- appLaunch -->
</testsuite> <!-- shreenidhiAndSushma -->
